############################# config ##############################

############################# 参数说明 #############################

# [package]
# install_msi_quiet(bool): 安装 msi 格式安装包时的运行模式（passive 或 quiet）

# [package.uninstall_packages]
# pkgs_name(list[str]): 需要卸载的程序包的名称

# [[package.install_packages]]
# src(str): msi 或 exe 安装包的绝对路径
# exe_dst(str): 安装的目标目录绝对路径（仅安装 exe 时可指定）
# ......

###################################################################

# [screen]
# set_type(str): 屏幕设置项得类型，dpi 或 resolution
# [screen.dpi]
# dpi(int): 要设置得屏幕缩放百分比得值
# [screen.resolution]
# width(int): 要设置得屏幕分辨率得宽度
# height(int): 要设置得屏幕分辨率得高度

###################################################################

# [mkdir]
# root_path(str): 要创建文件夹得绝对或相对路径（根目录，空字符串 = None = './'）
# dirnames_in_year(list[int|str]): 要创建得文件名（必须是年份格式，且列表中的值必须都是相同类型）

###################################################################

# [feed]
# prescription_fmt(list): 允许喂入的处方文件的格式
# src_dir(str): 需要喂入的处方的源目录绝对路径
# dst_dir(str): Yakushi 程序配置的处方 input 目录的绝对路径
# interval(int or list[int|float]): 每次喂处方的间隔时间，时间单位：秒
#   int: 配置为 int 时，间隔时间为 int 值的秒数
#   list: 配置为 list 时，间隔时间为 range(list[0], list[1]) 的随机秒数

# [feed.schedule]
# batch(bool): 是否批量喂入 feed.src_dir 目录下的处方文件
#   说明：
#     当 batch 为 true 时，识别 feed.src_dir 目录下的处方目录，将识别到的目录下的所有满足 feed.prescription_fmt 的处方文件复制到 feed.dst_dir 目录下，在 feed.src_dir 目录下识别到非目录格式得文件不做处理
#     当 batch 为 false 时，识别 feed.src_dir 目录下的处方文件，将所有满足 feed.prescription_fmt 的处方文件复制到 feed.dst_dir 目录下
# loop(bool): 是否循环喂入 feed.src_dir 目录下的处方文件
# last(int): 持续喂入处方的总时间，时间单位：秒
#   说明：
#     当 loop 为 true 时，last 时间生效，feed.src_dir 目录下的处方文件循环读取，last 时间结束程序停止运行
#     当 loop 为 true 且 last 为 -1 时，feed.src_dir 目录下的处方文件将永久循环读取，只可手动结束程序停止运行
#     当 loop 为 false 时，last 时间无效，feed.src_dir 目录下的处方文件消耗完毕则程序停止运行

###################################################################

# [nsips]
# src_dir(str): 要分析得 nsips 源数据存放得目录路径，相对路径或绝对路径皆可，Windows路径或Linux路径皆可（会根据实际的系统自动转换路径格式）
# output_xlsx_prefix(str): 输出得结果 Excel 文件名的前缀（后缀拼接了源数据目录名）
# res_sheet_name(str): 输出得结果 Excel 文件中的 sheet 名
#   说明：
#     结果文件包含了 Excel 文件及 html 文件，均会输出于 {src_dir}/result 目录下

###################################################################

[package]
install_msi_quiet = false

[package.uninstall_packages]
pkgs_name = ["薬師丸賢太"]

[[package.install_packages]]
src = 'D:\NeoX\pkgs\Yakumaru-A-1.9.7-Debug-2023122502.msi'

# [[package.install_packages]]
# src = "D:\\NeoX\\pkgs\\Yakumaru-A-1.9.8-Debug-2024012901.exe"
# exe_dst = "D:\\NeoX\\"

###################################################################

[screen]
set_type = "dpi"
[screen.dpi]
dpi = 100
[screen.resolution]
width = 2560
height = 1440

###################################################################

[mkdirs]
root_path = ''
dirnames_in_year = [2023, 2024]

###################################################################

[feed]
prescription_fmt = [".png", ".jpg", ".jpeg", ".pdf", ".tiff"]
src_dir = 'D:\NeoX\prescriptions'
dst_dir = 'D:\NeoX\in'
interval = 60
[feed.schedule]
batch = false
loop = true
last = 600

###################################################################

[nsips]
src_dir = './202408'
output_xlsx_prefix = "NSIPS_SUM"
res_sheet_name = "Trend"
