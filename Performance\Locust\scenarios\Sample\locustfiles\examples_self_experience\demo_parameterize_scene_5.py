#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
@Date       :   2022-01-08
<AUTHOR>   <PERSON><PERSON>L<PERSON>
@Email      :   <EMAIL>
@File       :   demo_parameterize_scene_5.py
@Software   :   PyCharm
"""
from locust import HttpUser, TaskSet, task, between
# 每个用户共享使用全局参数，不循环使用，序列调用完毕后抛出异常；使用全局列表，item = list.pop() 元素调用；如为全局字典则：item = dict['name].pop()
USER_CREDENTIALS = [
    ("user1", "password"),
    ("user2", "password"),
    ("user3", "password"),
]


class UserBehaviour(TaskSet):
    def on_start(self):
        if len(USER_CREDENTIALS) > 0:
            user, passw = USER_CREDENTIALS.pop()
            self.client.post("/login", {"username": user, "password": passw})

    @task
    def some_task(self):
        # user should be logged in here (unless the USER_CREDENTIALS ran out)
        self.client.get("/protected/resource")


class User(HttpUser):
    tasks = [UserBehaviour]
    wait_time = between(5, 60)