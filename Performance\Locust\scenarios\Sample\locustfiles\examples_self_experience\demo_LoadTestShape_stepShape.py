#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
@Date       :   2021-11-29
<AUTHOR>   <PERSON><PERSON><PERSON><PERSON>
@Email      :   <EMAIL>
@File       :   demo_LoadTestShape_stepShape
@Software   :   PyCharm
"""
import datetime
import math
from locust import LoadTestShape, task, constant, HttpUser


class MyUser(HttpUser):
    wait_time = constant(1)

    @task(1)
    def task_1(self):
        print("my_task!!!")


# 启动策略：逐步负载策略每隔30秒新增启动10个用户(每秒加载一个用户)，共运行10分钟
class MyCustomShape(LoadTestShape):
    """
        step_time -- 每个 step 下的执行时长
        step_load -- 每个 step 下增加的用户数
        spawn_rate -- 每个 step 下的用户加载率(即多少秒加载完指定用户数)
        time_limit -- 压测的执行总时长
    """
    step_time = 30
    step_load = 10
    spawn_rate = 10
    time_limit = 600

    def tick(self):
        run_time = self.get_run_time()
        if run_time > self.time_limit:
            return None
        current_step = math.floor(run_time / self.step_time) + 1
        return current_step * self.step_load, self.spawn_rate
