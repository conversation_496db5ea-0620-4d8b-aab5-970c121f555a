#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
@Date       :   2024-12-24
<AUTHOR>   <PERSON><PERSON>Lu
@Email      :   <EMAIL>
@File       :   general.py
@Software   :   Cursor
"""

from typing import Any, Dict, Mapping, Optional, Type

import requests
from loguru._logger import Logger

# Constants
SUCCESS_STATUS_CODE = 200
DEVICE_ENDPOINT = "/neox/vision/merchant/device"


def get_device_id(
    env: str,
    api_version: str,
    headers: Mapping[str, Any],
    log_flag: bool,
    clog: Optional[Type[Logger]],
) -> Dict[str, Any]:
    """
    Retrieve device ID from the NeoX vision merchant device endpoint.

    This function makes a POST request to the device management API to obtain
    a device ID that can be used for subsequent API calls in the prescription
    processing workflow.

    Args:
        env: Environment domain name (e.g., "api.example.com")
        api_version: API version string (e.g., "v1", "v2")
        headers: HTTP headers for the request
        log_flag: Whether to enable debug logging
        clog: Logger instance for debug output

    Returns:
        Dictionary containing the device API response data or error information.
        On success, returns the full API response. On failure, returns a dict
        with 'status_code' and 'msg' keys containing error details.

    Example:
        >>> headers = {"Authorization": "Bearer token123"}
        >>> result = get_device_id("api.example.com", "v1", headers, True, logger)
        >>> if result.get("status_code") == 200:
        ...     device_id = result.get("deviceId")

    Raises:
        requests.RequestException: For HTTP request-related errors
        ValueError: For JSON parsing errors
    """
    # 构建设备 ID 获取的完整 URL
    url_get_device_id = f"https://{env}/api/w/{api_version}{DEVICE_ENDPOINT}"

    try:
        # 发送 POST 请求获取设备 ID
        res_get_device_id = requests.post(
            url=url_get_device_id,
            json=None,
            headers=headers,
            verify=False,
            timeout=30,  # 添加超时设置
        )
        res_get_device_id.encoding = "utf-8"
        res_get_device_id_json = res_get_device_id.json()

        # 获取 HTTP 响应状态码
        resp_status_code = res_get_device_id.status_code

        # 记录调试日志
        if log_flag and clog:
            clog.debug(  # type: ignore
                f"[ get_device_id ] 的HTTP响应码为: [ {resp_status_code} ]; "
                f"响应体为: {res_get_device_id_json}"
            )

        # 根据状态码返回相应的结果
        if resp_status_code == SUCCESS_STATUS_CODE:
            return res_get_device_id_json
        else:
            return {"status_code": resp_status_code, "msg": res_get_device_id_json}

    except requests.RequestException as e:
        # 处理 HTTP 请求异常
        error_response = {"status_code": 0, "msg": f"Request failed: {str(e)}"}

        if log_flag and clog:
            clog.error(f"[ get_device_id ] 请求失败: {str(e)}")  # type: ignore

        return error_response

    except (ValueError, KeyError) as e:
        # 处理 JSON 解析异常
        error_response = {"status_code": 0, "msg": f"JSON parsing failed: {str(e)}"}

        if log_flag and clog:
            clog.error(f"[ get_device_id ] JSON解析失败: {str(e)}")  # type: ignore

        return error_response
