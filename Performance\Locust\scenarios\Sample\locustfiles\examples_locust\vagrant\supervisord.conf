[inet_http_server]          ; inet (TCP) server disabled by default
port=*:9001                 ; (ip_address:port specifier, *:port for all iface)
;username=user              ; (default is no username (open server))
;password=123               ; (default is no password (open server))

[supervisord]
logfile=/tmp/supervisord.log ; (main log file;default $CWD/supervisord.log)
logfile_maxbytes=50MB        ; (max main logfile bytes b4 rotation;default 50MB)
logfile_backups=10           ; (num of main logfile rotation backups;default 10)
loglevel=info                ; (log level;default info; others: debug,warn,trace)
pidfile=/tmp/supervisord.pid ; (supervisord pidfile;default supervisord.pid)
nodaemon=false               ; (start in foreground if true;default false)
minfds=1024                  ; (min. avail startup file descriptors;default 1024)
minprocs=200                 ; (min. avail process descriptors;default 200)

[supervisorctl]
serverurl=unix:///tmp/supervisor.sock ; use a unix:// URL  for a unix socket

[rpcinterface:supervisor]
supervisor.rpcinterface_factory = supervisor.rpcinterface:make_main_rpcinterface

[program:locustmaster]
command=locust --master -f examples/basic.py ; TODO host should perhaps be configurable through the web UI
process_name=master
autostart=true
directory=/vagrant
priority=1

[program:locustworkers]
command=locust --worker -f examples/basic.py ; TODO host should perhaps be configurable through the web UI
process_name=worker_%(process_num)s
numprocs=2
numprocs_start=1
autostart=true
priority=2
directory=/vagrant