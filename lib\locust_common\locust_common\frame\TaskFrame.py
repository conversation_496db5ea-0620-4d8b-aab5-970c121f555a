#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
@Date       :   2021-12-03
<AUTHOR>   <PERSON><PERSON><PERSON><PERSON>
@Email      :   <EMAIL>
@File       :   TaskFrame.py
@Software   :   PyCharm
"""

from pathlib import Path
from typing import Any, Callable, Dict, List, Mapping, Optional, Union

import orjson
from jmespath import search

# from loguru._logger import Logger

# Constants
HTTP_SUCCESS_CODE = "200"
CONTENT_TYPE_JSON = "json"
DEFAULT_LOG_LEVEL = "DEBUG"
LOG_FORMAT = "{time:YYYY-MM-DD HH:mm:ss.SSS} | {level} | {name}:{function}:{line} | {thread} | {message}"
LOG_ROTATION = "10MB"
LOG_RETENTION = 4


class FuncFrame:
    """
    A framework class for handling HTTP requests in Locust performance testing.

    This class provides templates for different types of HTTP requests including
    JSON responses, string responses, and file uploads with comprehensive
    logging and assertion capabilities.
    """

    __slots__ = (
        "url",
        "method",
        "name",
        "headers",
        "body",
        "assert_keys",
        "locust_logger",
        "log_flag",
        "log_level",
    )

    def __init__(
        self,
        method: str,
        name: str,
        url: str,
        headers: Mapping[str, Any],
        body: Any,
        assert_keys: Optional[List[Any]],
        locust_logger: Any,
        log_flag: bool,
        log_level: str = DEFAULT_LOG_LEVEL,
    ):
        """
        Initialize the FuncFrame with request parameters and logging configuration.

        Args:
            method: HTTP method (GET, POST, PUT, DELETE, etc.)
            name: Display name for the request in Locust reports
            url: Request URL endpoint
            headers: HTTP headers for the request
            body: Request body data
            assert_keys: Keys to validate in response for assertion
            locust_logger: Logger instance for request/response logging
            log_flag: Whether to enable detailed logging
            log_level: Logging level (DEBUG, INFO, WARNING, ERROR)
        """
        # 设置基本请求参数
        self.method = method.upper()
        self.name = name
        self.url = url
        self.headers = headers
        self.body = body
        self.assert_keys = assert_keys

        # 配置日志相关参数
        self.locust_logger = locust_logger
        self.log_flag = log_flag
        self.log_level = log_level

        # 如果启用日志，则配置logger
        if self.log_flag:
            self._configure_logger()

    def _configure_logger(self) -> None:
        """
        Configure the logger with specified settings.

        Sets up log file rotation, formatting, and other logging parameters
        based on the initialized configuration.
        """
        # 移除所有现有的logger处理器
        self.locust_logger.remove(handler_id=None)  # type: ignore

        # 添加新的logger配置
        self.locust_logger.add(
            sink="locust_{time:YYYY-MM-DD}.log",
            level=self.log_level,
            format=LOG_FORMAT,
            rotation=LOG_ROTATION,
            retention=LOG_RETENTION,
            backtrace=True,
            diagnose=True,
            encoding="utf-8",
        )  # type: ignore

    @staticmethod
    def _is_binary_str(context_bytes: bytes) -> bool:
        """
        Validate whether the response content is binary data.

        Args:
            context_bytes: Response content in bytes

        Returns:
            bool: True if content is binary, False if it's text
        """
        # 定义文本字符集合，用于判断内容是否为二进制
        text_chars = bytearray(
            {7, 8, 9, 10, 12, 13, 27} | set(range(0x20, 0x100)) - {0x7F}
        )
        # 通过字符转换检测是否为二进制内容
        return bool(context_bytes.translate(None, text_chars))

    def _log_request(self) -> None:
        """
        Log the request details including URL, headers, and body.

        Only logs when log_flag is enabled to avoid unnecessary overhead.
        """
        if self.log_flag:
            # 记录请求的详细信息
            self.locust_logger.debug(
                f"Trans name is: [ {self.name} ]; "
                f"Request url is: [ {self.url} ]; "
                f"Request header is: {self.headers}; "
                f"Request body is: {self.body};"
            )  # type: ignore

    def _log_response(
        self, status_code: int, response_content: Union[str, bytes]
    ) -> None:
        """
        Log the response details including status code and content.

        Args:
            status_code: HTTP response status code
            response_content: Response body content
        """
        if self.log_flag:
            # 记录响应的详细信息
            self.locust_logger.debug(
                f"Response code is: [ {status_code} ]; Response body is: {response_content}"
            )  # type: ignore

    def _handle_response_failure(
        self, resp, response_content: Union[str, bytes]
    ) -> None:
        """
        Handle response failure with unified error logging and reporting.

        Args:
            resp: Locust response object
            response_content: Response body content for error reporting
        """
        # 统一处理响应失败的情况，记录详细的错误信息
        resp.failure(
            f"FailTrans is: [ {self.name} ]; "
            f"Fail request url is: [ {self.url} ]; "
            f"Fail request header is: {self.headers}; "
            f"Fail request body is: {self.body}; "
            f"Fail response code is: [ {resp.status_code} ]; "
            f"Fail response body is: {response_content}"
        )

    def get_request_log(self) -> None:
        """
        Get request log information (legacy method for backward compatibility).

        This method maintains compatibility with existing code that calls
        this method directly.
        """
        # 保持向后兼容性的请求日志方法
        self._log_request()

    def task_assert_resp_by_str(
        self, obj, decode_enable: bool = True
    ) -> Union[str, bytes]:
        """
        Task template for handling HTTP requests with string response parsing.

        Args:
            obj: Locust user object with HTTP client
            decode_enable: Whether to decode binary response content

        Returns:
            Response content as string or bytes depending on decode_enable
        """
        # 准备请求数据
        if self.body is not None:
            # 根据content type处理请求体
            data = (
                orjson.dumps(self.body).decode()
                if CONTENT_TYPE_JSON in str(self.headers)
                else self.body
            )
            # 确保数据为bytes格式
            if not isinstance(data, bytes):
                data = data.encode("utf-8")
        else:
            data = None

        # 记录请求日志
        self._log_request()

        # 发送HTTP请求并处理响应
        with obj.client.request(
            method=self.method,
            name=self.name,
            url=self.url,
            headers=self.headers,
            data=data,
            catch_response=True,
        ) as resp:
            # 根据是否需要解码选择不同的响应处理方式
            if decode_enable:
                self.parse_resp_by_str(resp)
            else:
                self.parse_resp_without_decode(resp)

        # 返回响应内容，根据decode_enable参数决定是否解码
        return (
            resp.content.decode()
            if all([decode_enable, not self._is_binary_str(resp.content)])
            else resp.content
        )

    def task_assert_resp_by_json(self, obj) -> Optional[str]:
        """
        Task template for handling HTTP requests with JSON response parsing.

        Args:
            obj: Locust user object with HTTP client

        Returns:
            Response content as decoded string
        """
        # 将请求体转换为JSON格式的bytes
        data = orjson.dumps(self.body).decode().encode("utf-8")

        # 记录请求日志
        self._log_request()

        # 发送HTTP请求并解析JSON响应
        with obj.client.request(
            method=self.method,
            name=self.name,
            url=self.url,
            headers=self.headers,
            data=data,
            catch_response=True,
        ) as resp:
            # 使用JSON解析方式处理响应
            self.parse_resp_by_json(resp)
        return resp.content.decode()

    def task_assert_resp_with_file_upload(
        self,
        obj,
        file_path: Union[str, Path],
        file_field_name: str = "file",
        content_type: str = "application/octet-stream",
        form_data: Optional[Dict[str, Any]] = None,
        custom_assert_func: Optional[Callable[[Any], bool]] = None,
    ) -> Union[str, bytes]:
        """
        Task template for handling multipart/form-data file upload requests.

        This method provides a generic way to upload files with additional form data
        and custom assertion logic for response validation.

        Args:
            obj: Locust user object with HTTP client
            file_path: Path to the file to be uploaded
            file_field_name: Form field name for the file (default: "file")
            content_type: MIME type of the file (default: "application/octet-stream")
            form_data: Additional form data to be sent with the file
            custom_assert_func: Custom function to assert response validity

        Returns:
            Response content as string or bytes

        Raises:
            FileNotFoundError: If the specified file does not exist
            Exception: For other file upload related errors
        """
        # 将文件路径转换为Path对象以便处理
        file_path = Path(file_path)

        # 检查文件是否存在
        if not file_path.exists():
            raise FileNotFoundError(f"File not found: {file_path}")

        try:
            # 打开文件并准备上传
            with file_path.open(mode="rb") as f:
                # 构造multipart/form-data格式的文件数据
                files = {file_field_name: (file_path.name, f.read(), content_type)}

                # 记录文件上传请求的详细信息
                if self.log_flag:
                    self.locust_logger.debug(
                        f"Trans name is: [ {self.name} ]; "
                        f"Request url is: [ {self.url} ]; "
                        f"Request header is: {self.headers}; "
                        f"Request files is: {file_field_name}={file_path.name}; "
                        f"Request form data is: {form_data};"
                    )  # type: ignore

                # 发送文件上传请求
                with obj.client.request(
                    method=self.method,
                    name=self.name,
                    url=self.url,
                    files=files,
                    data=form_data,
                    headers=self.headers,
                    catch_response=True,
                ) as resp:
                    # 使用自定义断言函数或默认的状态码检查
                    if custom_assert_func:
                        # 调用自定义断言函数验证响应
                        assert_result = custom_assert_func(resp)
                    else:
                        # 使用默认的HTTP状态码检查
                        assert_result = str(resp.status_code) == HTTP_SUCCESS_CODE

                    # 处理响应内容，判断是否需要解码
                    resp_content = (
                        resp.content.decode()
                        if all([resp.content, not self._is_binary_str(resp.content)])
                        else resp.content
                    )

                    # 记录响应日志
                    self._log_response(resp.status_code, resp_content)

                    # 根据断言结果设置响应状态
                    if assert_result:
                        resp.success()
                    else:
                        self._handle_response_failure(resp, resp_content)

                return resp_content

        except Exception as e:
            # 捕获并记录文件上传过程中的异常
            error_msg = f"Error uploading file {file_path}: {e}"
            if self.log_flag:
                self.locust_logger.error(error_msg)  # type: ignore
            raise

    def parse_resp_without_decode(self, resp) -> None:
        """
        Parse response without decoding binary content.

        This method is suitable for handling binary responses or when
        decoding is not necessary.

        Args:
            resp: Locust response object
        """
        # 检查HTTP状态码是否成功
        check_status_code = str(resp.status_code) == HTTP_SUCCESS_CODE
        # 直接使用原始响应内容，不进行解码
        resp_content = resp.content

        # 记录响应日志
        self._log_response(resp.status_code, resp_content)

        # 根据状态码检查结果设置响应状态
        if check_status_code:
            resp.success()
        else:
            self._handle_response_failure(resp, resp_content)

    def parse_resp_by_str(self, resp) -> None:
        """
        Parse response by converting content to string and performing assertions.

        This method decodes response content and validates it against
        configured assertion keys.

        Args:
            resp: Locust response object
        """
        # 检查HTTP状态码是否成功
        check_status_code = str(resp.status_code) == HTTP_SUCCESS_CODE

        # 处理响应内容，如果不是二进制则解码为字符串
        resp_content = (
            resp.content.decode()
            if all([resp.content, not self._is_binary_str(resp.content)])
            else resp.content
        )

        # 执行断言检查，验证响应内容是否包含预期的键值
        assert_value = (
            all(each_key in resp_content for each_key in self.assert_keys)
            if isinstance(self.assert_keys, list) and self.assert_keys
            else True
        )

        # 记录响应日志
        self._log_response(resp.status_code, resp_content)

        # 根据状态码和断言结果设置响应状态
        if check_status_code and assert_value:
            resp.success()
        else:
            self._handle_response_failure(resp, resp_content)

    def parse_resp_by_json(self, resp) -> Optional[Dict[str, Any]]:
        """
        Parse response as JSON and perform JMESPath-based assertions.

        This method is designed for REST API responses that return JSON data
        and need complex validation using JMESPath expressions.

        Args:
            resp: Locust response object

        Returns:
            Parsed JSON response as dictionary, or None if parsing fails
        """
        # 检查HTTP状态码是否成功
        check_status_code = str(resp.status_code) == HTTP_SUCCESS_CODE

        # 解码响应内容为字符串
        resp_content = (
            resp.content.decode() if resp.content is not None else resp.content
        )

        # 记录响应日志
        self._log_response(resp.status_code, resp_content)

        # 检查响应内容是否为空
        if len(resp_content) > 0:
            # 将响应内容解析为JSON对象
            response_dict = orjson.loads(resp_content)

            # 使用JMESPath表达式进行断言检查
            assert_value = (
                all(
                    search(each_key[0], response_dict) == each_key[1]
                    for each_key in self.assert_keys
                )
                if isinstance(self.assert_keys, list) and self.assert_keys
                else True
            )

            # 根据状态码和断言结果设置响应状态
            if check_status_code and assert_value:
                resp.success()
            else:
                self._handle_response_failure(resp, resp_content)
            return response_dict
        else:
            # 响应内容为空时标记为失败
            self._handle_response_failure(resp, resp_content)
            return None
