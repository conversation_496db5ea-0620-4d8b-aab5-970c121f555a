# -*- coding: utf-8 -*-
"""
@Date       :   2024-02-01
<AUTHOR>   <PERSON><PERSON>Lu
@Email      :   <EMAIL>
@File       :   mapping.py
@Software   :   PyCharm
"""

import random
import shutil
import time
from itertools import chain, repeat
from pathlib import Path, PurePath
from typing import List, Mapping, Type

import petl as etl
from jmespath import search
from loguru import logger
from loguru._logger import Logger
from scopes.nsips import NSIPSETL, NSIPSCharts
from scopes.windows import (
    exec_setdpi,
    get_dpi,
    get_real_resolution,
    install_exe,
    install_msi,
    set_screen_resolution,
    uninstall_pkg,
)

from common.clog import set_log
from common.configuration import console, console_log
from common.create_dirs import create_year_structure
from common.variables import ActionItems, ScopeItems

ACTION = ActionItems()
SCOPE = ScopeItems()


class ActionScope:
    """
    Customized mapping class for target action scope.
    """

    __slots__ = ["data", "action", "scope", "path"]

    def __init__(self, data: Mapping[str, str] | None, path: Path | None = None):
        """
        Initialize the ActionScope class.

        Args:
            data (Mapping[str, str] | None): Config data.
            path (str): Demo config files path.
        """
        self.data = data
        self.path = path

    @staticmethod
    def _rewrite_file(src_path: Path, dst_path: Path) -> Path | None:
        """
        Reads a file's binary content and writes it to a new file,
        ensuring new metadata.
        """
        try:
            with open(src_path, "rb") as f_in:
                content = f_in.read()
            with open(dst_path, "wb") as f_out:
                f_out.write(content)
            return dst_path
        except IOError as e:
            console.print(
                f"[bold red]Error processing file {src_path.name}: {e}[/bold red]"
            )
            return None

    @staticmethod
    def _iter_dispose_logic(
        batch: bool,
        fmts: list[str],
        items: list[Path],
        dst_dir: Path,
        secs: int | list[int],
        mylog: Type[Logger],
    ):
        """
        A static method to iterate through a list of files and copy them to a destination directory.

        Args:
            batch (bool): Whether or not to iterate over batch.
            fmts (list[str]): List of file formats to be copied.
            items (list[Path]): List of files to be copied.
            dst_dir (Path): Destination directory path.
            secs (int | list[int]): Sleep interval in seconds or a range of seconds.
            mylog (logger): loguru logger.

        Yields:
            Path: Path of the copied file.
        """
        interval_type = type(secs)
        mylog.info(f"feed.interval type is -> {interval_type}")

        console.print("""[bold cyan]Checking input files...[/bold cyan]""")
        filter_items = (
            [item for item in items if item.is_dir()]
            if batch
            else [item for item in items if item.is_file()]
        )
        console.print(f"""[bold cyan]Check complete.The input files or batch file dirs count is: <{len(filter_items)}>[/bold cyan]
[bold red]Notice: The root dir contains a log file with more details.[/bold red]""")

        if filter_items:
            for item in filter_items:
                if batch:
                    batch_files_path = list(item.iterdir())
                    batch_count = 0
                    for batch_file in batch_files_path:
                        if batch_file.suffix in fmts:
                            batch_count += 1
                            dst_file_path = dst_dir.joinpath(batch_file.name)
                            cp_path = ActionScope._rewrite_file(
                                batch_file, dst_file_path
                            )
                            if not cp_path:
                                mylog.error(f"Failed to rewrite file {batch_file.name}")
                                continue

                            mylog.info(f"[{batch_count}] - [ {cp_path} ] is feed.")
                            yield cp_path
                    mylog.debug(
                        f"The batch processing files is: {
                            [
                                bf.parts[-1]
                                for bf in batch_files_path
                                if bf.suffix in fmts
                            ]
                        }"
                    )
                    match secs:
                        case int():
                            mylog.info(
                                f"Next batch prescription will be feed after {secs} seconds..."
                            )
                            time.sleep(secs)
                        case list():
                            random_secs = random.randint(secs[0], secs[1])
                            mylog.info(
                                f"Next batch prescription will be feed after {random_secs} seconds..."
                            )
                            time.sleep(random_secs)
                        case _:
                            console.print(f"""[bold red]Error feed configuration![/bold red] 
        [cyan]feed.interval: [/cyan][u]{secs}[/u]
        Set interval to 60s.""")
                            mylog.warning(
                                f"Error feed configuration! feed.interval: [ {secs} ]... Auto Set interval to 60s"
                            )
                            time.sleep(60)
                else:
                    if item.suffix in fmts:
                        dst_file_path = dst_dir.joinpath(item.name)
                        cp_path = ActionScope._rewrite_file(item, dst_file_path)
                        if not cp_path:
                            mylog.error(f"Failed to rewrite file {item.name}")
                            continue

                        mylog.info(f"[ {cp_path} ] is feed.")

                        match secs:
                            case int():
                                mylog.info(
                                    f"Next prescription will be feed after {secs} seconds..."
                                )
                                time.sleep(secs)
                                yield cp_path
                            case list():
                                random_secs = random.randint(secs[0], secs[1])
                                mylog.info(
                                    f"Next prescription will be feed after {random_secs} seconds..."
                                )
                                time.sleep(random_secs)
                                yield cp_path
                            case _:
                                console.print(f"""[bold red]Error feed configuration![/bold red] 
        [cyan]feed.interval: [/cyan][u]{secs}[/u]
        Set interval to 60s.""")
                                mylog.warning(
                                    f"Error feed configuration! feed.interval: [ {secs} ]... Auto Set interval to 60s"
                                )
                                time.sleep(60)
                                yield cp_path
        else:
            return None

    def feed_prescriptions(self) -> None:
        """
        Feed the Yakushi with prescriptions automatically.
        """
        feed_logger = set_log(True, logger, "TRACE")
        feed_config = search("feed", self.data)

        if feed_config:
            fmt_set = search("prescription_fmt", feed_config)
            src_dir = search("src_dir", feed_config)
            dst_dir = search("dst_dir", feed_config)
            interval = search("interval", feed_config)

            batch = search("schedule.batch", feed_config)
            loop = search("schedule.loop", feed_config)
            last = search("schedule.last", feed_config)

            if all([Path(d).exists() for d in (src_dir, dst_dir)]):
                src_dir, dst_dir = Path(src_dir), Path(dst_dir)
                src_dir_items_set = list(Path(src_dir).iterdir())
                dispose_count = 0

                if loop:
                    if last == -1:
                        for _ in repeat(None):
                            each_dispose_path = self._iter_dispose_logic(
                                batch,
                                fmt_set,
                                src_dir_items_set,
                                dst_dir,
                                interval,
                                feed_logger,
                            )
                            for p in each_dispose_path:
                                dispose_count += 1
                                feed_logger.debug(
                                    f"The prescription file path is: [ {p} ]"
                                )
                                feed_logger.trace(
                                    f"[ {dispose_count} ] prescriptions were disposed!"
                                )

                    elif last >= 0:
                        begin_time = time.perf_counter()
                        for _ in repeat(None):
                            each_dispose_path = self._iter_dispose_logic(
                                batch,
                                fmt_set,
                                src_dir_items_set,
                                dst_dir,
                                interval,
                                feed_logger,
                            )
                            for p in each_dispose_path:
                                time_elapsed = int(time.perf_counter() - begin_time)
                                dispose_count += 1
                                feed_logger.debug(
                                    f"The prescription file path is: [ {p} ]"
                                )
                                feed_logger.trace(
                                    f"[ {dispose_count} ] prescriptions were disposed!"
                                )
                                if time_elapsed >= last:
                                    console.print("[bold red]Time's up![/bold red]")
                                    feed_logger.info(
                                        f"Time's up! The target running time is {last} seconds. It has been running for {time_elapsed} seconds."
                                    )
                                    return
                else:
                    each_dispose_path = self._iter_dispose_logic(
                        batch,
                        fmt_set,
                        src_dir_items_set,
                        dst_dir,
                        interval,
                        feed_logger,
                    )
                    for p in each_dispose_path:
                        dispose_count += 1
                        feed_logger.debug(f"The prescription file path is: [ {p} ]")
                        feed_logger.trace(
                            f"[ {dispose_count} ] prescriptions were disposed!"
                        )

            else:
                console.print("""[bold red]Error feed configuration![/bold red] 
[cyan]feed: [/cyan][u]Pls confirm your dirs if exists.[/u]""")
                feed_logger.error(
                    "Error feed configuration! Pls confirm your dirs if exists."
                )
        else:
            console.print(f"""[bold red]Error feed configuration![/bold red] 
[cyan]feed: [/cyan][u]{feed_config}[/u]""")
            feed_logger.error(f"Error feed configuration! feed: {feed_config}")

    def install_pkgs(self) -> None:
        """
        Install Windows msi or exe pkgs.
        """
        rsp_outs, rsp_errs = None, None

        install_packages = search("package.install_packages", self.data)
        install_msi_setting = search("package.install_msi_quiet", self.data)
        install_msi_quiet = (
            install_msi_setting
            if (
                install_msi_setting is not None
                and isinstance(install_msi_setting, bool)
            )
            else False
        )

        if install_packages:
            for each_pkg_data in install_packages:
                if isinstance(each_pkg_data, dict) and all(
                    [key in ("src",) for key in each_pkg_data]
                ):
                    src_file = each_pkg_data["src"]
                    src_fmt = PurePath(each_pkg_data["src"]).suffix
                    match src_fmt:
                        case ".msi":
                            rsp_outs, rsp_errs = install_msi(
                                src_file, install_msi_quiet
                            )
                        case ".exe":
                            key = "exe_dst"
                            if key in each_pkg_data:
                                dst_dir = each_pkg_data[key]
                                rsp_outs, rsp_errs = install_exe(src_file, dst_dir)
                            else:
                                console.print("""[bold red]Error configuration![/bold red]
[red]No key: exe_dst[/red]""")
                        case _:
                            rsp_errs = f"Error format! src_fmt is: [{src_fmt}]"
                    console_log(rsp_outs, rsp_errs)
        else:
            console.print(f"""[bold red]Error configuration![/bold red]
[cyan]install_packages: [/cyan][u]{install_packages}[/u]""")

    def uninstall_pkgs(self) -> None:
        """
        Uninstall Windows pkgs.
        """
        uninstall_packages_name = search(
            "package.uninstall_packages.pkgs_name", self.data
        )

        if uninstall_packages_name and isinstance(uninstall_packages_name, list):
            for each_pkg_name in uninstall_packages_name:
                rsp_outs, rsp_errs = uninstall_pkg(each_pkg_name)
                console_log(rsp_outs, rsp_errs)

    @staticmethod
    def _gen_nsips_charts(
        input_files: List[Path],
        output_file: Path | None = None,
        res_sheet_name: str | None = None,
    ) -> None:
        """
        A static method to generate charts of NSIPS.(Internal)
        """
        NE = NSIPSETL()
        nsips_tables, nsips_each, nsips_sum, nsips_titles = [], [], [], []
        for input_file in input_files:
            nsips_data = NE.parse_nsips_table(input_file)
            if nsips_data[0]:
                nsips_tables.append(nsips_data[1][0])
                nsips_titles.append(nsips_data[1][3])

        agg_nsips_table = NE.aggregate_parsed_nsips_table(nsips_tables)
        # print(etl.lookall(agg_nsips_table))
        res_nsips_table = NE.move_sum_row_to_bottom(agg_nsips_table)
        # print(etl.lookall(res_nsips_table))

        etl.toxlsx(tbl=res_nsips_table, filename=output_file, sheet=res_sheet_name)

        console.print(f"""[bold green]Checking src xlsx if exists...[/bold green]
[u]{output_file}[/u]""")
        for _ in repeat(None):
            if output_file and output_file.exists():
                break
        console.print("""[bold green]Src xlsx is exists. Continuing...[/bold green]""")

        NC = NSIPSCharts(
            source=output_file, nsips_date=nsips_titles, res_path=output_file.parent
        )
        NC.gen_chart_line()

    def get_nsips_charts(self) -> None:
        """
        Get NSIPS charts.
        """
        nsips_config = search("nsips", self.data)

        if nsips_config:
            input_files = []
            tar_dir_path = Path(nsips_config["src_dir"]).resolve()
            last_dir_name = tar_dir_path.parts[-1]

            for file_path in tar_dir_path.glob("*.xlsx"):
                input_files.append(file_path)

            input_files = sorted(
                input_files, key=lambda x: Path(x).stem.split("(")[1].split(")")[0]
            )

            res_dir_path = tar_dir_path.joinpath("result")
            res_dir_path.mkdir(exist_ok=True, parents=True)

            output_file_name = (
                f"{nsips_config['output_xlsx_prefix']}-{last_dir_name}.xlsx"
            )
            output_file = res_dir_path.joinpath(output_file_name)

            self._gen_nsips_charts(
                input_files, output_file, nsips_config["res_sheet_name"]
            )
        else:
            console.print("""[bold red]Error nsips configuration![/bold red] 
[cyan]nsips: [/cyan][u]Your nsips configuration is none.[/u]""")

    def generate_templates(self) -> None:
        """
        Generate config templates.
        """
        demo_file_names = ("template_config.toml",)
        target_file_path = Path(self.path) if self.path is not None else Path.cwd()

        if target_file_path.exists():
            files_path = (
                (
                    Path(__file__).parent.parent.joinpath("templates").joinpath(file),
                    target_file_path.joinpath(file),
                )
                for file in demo_file_names
            )
            for path_set in files_path:
                dst_file_path = path_set[1]
                if dst_file_path.exists():
                    dst_file_path.unlink()
                dst_path = shutil.copyfile(str(path_set[0]), str(dst_file_path))
                console.print(f"""[bold green]The destination path is: [/bold green]
[u]{dst_path}[/u]""")
        else:
            console.print(f"""[bold red]Target file path is not exists.[/bold red]
[cyan]Path: [/cyan][u]{target_file_path}[/u]""")

    def _set_dpi(self) -> None:
        """
        Set Windows DPI.
        """
        win_dpi_config = search("screen.dpi", self.data)
        tar_dpi = int(win_dpi_config["dpi"])
        cur_dpi = int(get_dpi())
        console.print(f"""[bold green]Current DPI: <{cur_dpi}%>[/bold green]""")
        if cur_dpi == tar_dpi:
            console.print(f"""[bold green]DPI is: <{tar_dpi}%>[/bold green]""")
        else:
            console.print(
                f"""[bold green]Updating DPI...The target DPI is: <{tar_dpi}%>[/bold green]"""
            )
            set_dpi_exe = (
                Path(__file__).parent.parent.joinpath("bin").joinpath("SetDpi.exe")
            )
            exec_status = exec_setdpi(set_dpi_exe.as_posix(), tar_dpi)
            console.print("""[bold green]WIP...Pls wait...[/bold green]""")
            time.sleep(3)
            if not exec_status:
                console.print(
                    f"""[bold red]Failed to update DPI to: <{tar_dpi}%>[/bold red]"""
                )
            else:
                console.print(
                    f"""[bold green]DPI has been updated to: <{tar_dpi}%>[/bold green]"""
                )

    def _set_resolution(self) -> None:
        """
        Set Windows resolution.
        """
        win_resolution_config = search("screen.resolution", self.data)
        w, h = int(win_resolution_config["width"]), int(win_resolution_config["height"])
        console.print(
            f"""[bold green]Updating screen resolution to: <{w} * {h}>[/bold green]"""
        )
        set_screen_resolution([w, h])
        console.print("""[bold green]Screen resolution has been set.[/bold green]""")

    def set_screen(self):
        """
        Set Windows screen(DPI or resolution).
        """
        screen_config = search("screen", self.data)
        if screen_config:
            tar_screen_type = screen_config["set_type"]

            match tar_screen_type.lower():
                case "dpi":
                    self._set_dpi()
                case "resolution":
                    self._set_resolution()
                case _:
                    console.print(f"""[bold red]ScreenType setting error.[/bold red]
[cyan]screen.set_type: [/cyan][u]{tar_screen_type}[/u]""")
        else:
            console.print(
                """[bold red]Error screen configuration! Your config is None.[/bold red]"""
            )

    @staticmethod
    def _get_dpi() -> None:
        """
        Get Windows DPI.
        """
        screen_scale_rate = get_dpi()
        console.print(f"""[bold green]DPI: <{screen_scale_rate}>[/bold green]""")

    @staticmethod
    def _get_resolution() -> None:
        """
        Get Windows resolution.
        """
        w, h = get_real_resolution()
        console.print(
            f"""[bold green]Resolution(width, height): <{w}, {h}>[/bold green]"""
        )

    def get_screen(self, screen_type: int | str):
        """
        Get Windows screen(DPI or resolution).
        """
        sc_type = None

        match screen_type:
            case int():
                sc_type_map = {
                    0: "dpi",
                    1: "resolution",
                }
                sc_type = sc_type_map[screen_type]
            case str():
                sc_type = screen_type
            case _:
                console.print(f"""[bold red]ScreenType type error.[/bold red]
[cyan]ScreenType type: [/cyan][u]{type(screen_type)}[/u]""")

        match sc_type.lower():
            case "dpi":
                self._get_dpi()
            case "resolution":
                self._get_resolution()
            case _:
                console.print(f"""[bold red]ScreenType content error.[/bold red]
[cyan]ScreenType content: [/cyan][u]{screen_type}[/u]""")

    def make_dirs(self):
        """
        Create structured dirs under the target path.
        """
        mkdirs_config = search("mkdirs", self.data)

        if mkdirs_config:
            base_path = search("root_path", mkdirs_config)
            root_path = (
                base_path
                if base_path is not None and isinstance(base_path, str)
                else "./"
            )
            mk_dirs = search("dirnames_in_year", mkdirs_config)
            if isinstance(mk_dirs, list) and all(
                [isinstance(mk_dir, (int, str)) for mk_dir in mk_dirs]
            ):
                root_path = Path(root_path)
                if root_path.exists():
                    for mk_dir in mk_dirs:
                        create_year_structure(
                            root_path,
                            mk_dir if isinstance(mk_dir, int) else int(mk_dir),
                        )
                else:
                    console.print("""[bold red]Error base path configuration![/bold red]
[red]The path is not exists: root_path[/red]""")
            else:
                console.print("""[bold red]Error mkdirs configuration![/bold red]
[red]The format must be: List[int][/red]""")
        else:
            console.print(f"""[bold red]Error configuration![/bold red] 
[cyan]mkdir: [/cyan][u]{mkdirs_config}[/u]""")

    def select(self, action: str, scope: str) -> None:
        """
        Select target function in mapping handlers.

        Args:
            action (str): Target action.
            scope (str): Target scope.

        Raises:
            Exception: If the scope is not valid.
        Returns:
            None: None.
        """

        handlers = {
            ACTION.get: {
                SCOPE.windows: [
                    self._get_dpi,
                    self._get_resolution,
                ],
                SCOPE.nsips: [
                    self.get_nsips_charts,
                ],
            },
            ACTION.set: {
                SCOPE.windows: [
                    self._set_dpi,
                    self._set_resolution,
                ],
            },
            ACTION.feed: {
                SCOPE.windows: [
                    self.feed_prescriptions,
                ],
            },
            ACTION.install: {
                SCOPE.windows: [
                    self.install_pkgs,
                ],
            },
            ACTION.uninstall: {
                SCOPE.windows: [
                    self.uninstall_pkgs,
                ],
            },
            ACTION.mkdirs: {
                SCOPE.windows: [
                    self.make_dirs,
                ],
            },
            ACTION.generate: {
                SCOPE.template: [
                    self.generate_templates,
                ],
            },
        }
        # The expression mean: Get handlers[*].keys(), then merge into set
        if scope not in set(chain(*[list(k.keys()) for k in search("*", handlers)])):
            raise Exception(f"Config Error! Your scope is : [{scope}]")

        handlers[action][scope]()
