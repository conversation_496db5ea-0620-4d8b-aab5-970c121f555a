# NeoXMetrics

一个将指标写入 Prometheus 的 CLI 工具。

## 功能特性

- **Prometheus 指标监控**：从 Redis TimeSeries 读取数据并暴露为 Prometheus 指标
- **RTS 数据处理**：Redis TimeSeries 数据汇总、转存和对比功能
- **日本时区支持**：所有时间计算使用日本时区 (Asia/Tokyo)
- **定时任务**：支持每分钟和每小时的自动化数据处理

# 安装配置

```bash
cd testing/Tools/NeoXMetrics

# 设置 Python 环境
pip install -r requirements.txt

# 以下命令用于构建可执行二进制文件
# 运行以下命令前请先配置 neox_metrics.spec
pyinstaller neox_metrics.spec

# 或者你也可以使用以下命令构建
# neox_metrics.spec 配置与以下命令相同（请更新 {your_env_path} ！！！）
pyinstaller --clean --onefile neox_metrics.py
```

# 使用用法

1. 按需配置与 neox_metrics 可执行文件同级目录下的 metrics.toml 文件；
2. 执行 neox_metrics 脚本程序，操作如下：
    ```shell
    cd /your/path/to/NeoXMetrics
    nohup ./neox_metrics > neox_metrics.log 2>&1 &
    ```
3. 在 Prometheus 的配置文件中，添加对 neox_metrics 程序暴露的 HTTP 端点的抓取配置，例如：
   ```yaml
   scrape_configs:
   - job_name: 'neox_metrics'
     static_configs:
       - targets: ['localhost:7333']
   ```
   将此配置添加到 Prometheus 的 prometheus.yml 文件中，并重启 Prometheus，Prometheus 就会开始定期抓取 http://localhost:7333/metrics 上暴露的指标数据。

# RTS 监控功能

NeoXMetrics 现在支持 Redis TimeSeries (RTS) 数据处理功能，包括：

- **每分钟数据汇总**：从源 key 查询当前小时的数据之和并写入目标 key
- **每小时对比数据**：查询上周同日同时间的数据并写入对比 key
- **日本时区支持**：所有时间计算使用 Asia/Tokyo 时区

详细使用说明请参考：**[RTS 监控功能文档](RTS_MONITORING_README.md)**

## RTS 监控快速开始

1. **创建必要的 TimeSeries 键**：
   ```bash
   redis-cli
   TS.CREATE GRAFANA_NEOX_ENGINE_REQUEST_COUNT
   TS.CREATE GRAFANA_SDE_PRES_COUNT_TODAY
   TS.CREATE GRAFANA_SDE_PRES_COUNT_LAST_WEEKDAY
   ```

2. **测试连接**：
   ```bash
   python test_redis_connection.py
   ```

3. **启动监控**：
   ```bash
   # 启动完整监控（包括 RTS 和 Prometheus）
   python neox_metrics.py

   # 或仅测试 RTS 功能
   python test_rts_monitoring.py
   ```

# 文档

- **[RTS 监控功能详细文档](RTS_MONITORING_README.md)** - Redis TimeSeries 数据处理功能的完整说明
- **[配置文件说明](metrics.toml)** - 配置参数详解
- **[测试工具](test_redis_connection.py)** - Redis 连接和 TimeSeries 模块测试

# 项目结构

```
Tools/NeoXMetrics/
├── neox_metrics.py              # 主程序
├── metrics.toml                 # 配置文件
├── test_rts_monitoring.py       # RTS 功能测试
├── test_redis_connection.py     # Redis 连接测试
├── README.md                    # 主文档
├── RTS_MONITORING_README.md     # RTS 功能详细文档
├── requirements.txt             # Python 依赖
├── neox_metrics.spec           # PyInstaller 配置
├── neox_metrics.sh             # 启动脚本
└── common/
    └── configuration.py         # 配置文件加载工具
```
