#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
@Date       :   2021-11-18
<AUTHOR>   KunoLu
@Email      :   <EMAIL>
@File       :   DQueue.py
@Software   :   PyCharm
"""

import queue
from pathlib import Path
from typing import Dict, List


def data_queue(param_file: Path) -> queue.Queue:
    """
    Create data queue from parameter file.

    Reads a CSV-formatted parameter file and creates a queue containing
    dictionaries representing each row of data. The first row is treated
    as column headers.

    Args:
        param_file: Absolute path to the parameter file (CSV format)

    Returns:
        Queue object populated with parameter dictionaries

    Example:
        >>> param_file = Path("/path/to/params.csv")
        >>> # File content:
        >>> # username,password
        >>> # user1,pass1
        >>> # user2,pass2
        >>> q = data_queue(param_file)
        >>> data = q.get_nowait()
        >>> print(data)  # {"username": "user1", "password": "pass1"}

    Raises:
        FileNotFoundError: If the parameter file does not exist
        ValueError: If the file format is invalid or rows have mismatched columns
    """
    # 读取参数文件内容
    with param_file.open("r", encoding="utf-8") as f:
        param_list = f.readlines()

    # 解析列头（第一行）
    key_list = param_list[0].strip().split(",")
    param_dict_in_list: List[Dict[str, str]] = []

    # 解析数据行（从第二行开始）
    for i, v in enumerate(param_list):
        if i > 0:  # 跳过标题行
            value_list = v.strip().split(",")
            # 确保列数匹配
            if len(key_list) == len(value_list):
                param_dict_in_list.append(dict(zip(key_list, value_list)))

    # 创建并填充队列
    user_data_queue: queue.Queue = queue.Queue()
    if param_dict_in_list:
        for data in param_dict_in_list:
            user_data_queue.put_nowait(data)

    return user_data_queue
