version: "3.8"

services:
  postgres:
    image: timescale/timescaledb:latest-pg13
    container_name: timescaledb
    restart: unless-stopped
    # networks:
    #   - timescale-net
    ports:
      - "5432:5432"
    environment:
      POSTGRES_USER: postgres
      POSTGRES_PASSWORD: postgres@neox
      POSTGRES_DB: perf
      TZ: Asia/Shanghai
    volumes:
      volumes:
      - timescale_data:/var/lib/postgresql/data
      # - ./timescaledb/data:/var/lib/postgresql/data
      - ./timescaledb/sql/01_timescale_schema_locust-plugins.sql:/docker-entrypoint-initdb.d/01_timescale_schema_locust-plugins.sql
      - ./timescaledb/sql/02_zz_hypertable_locust-plugins.sql:/docker-entrypoint-initdb.d/02_zz_hypertable_locust-plugins.sql
    deploy:
      resources:
        limits:
          cpus: "2.0"
          memory: 4G
        reservations:
          cpus: "1.5"
          memory: 3G

  grafana:
    image: grafana/grafana:12.0.1
    container_name: grafana
    restart: unless-stopped
    user: root
    privileged: true
    ports:
      - "3001:3000"
    environment:
       GF_SECURITY_ADMIN_USER: admin
       GF_SECURITY_ADMIN_PASSWORD: Admin@Grafana
       GF_USERS_ALLOW_SIGN_UP: false
    # volumes:
    #   - "./grafana/data:/var/lib/grafana"
    #   - "./grafana/grafana.ini:/etc/grafana/grafana.ini"
    deploy:
      resources:
        limits:
          cpus: "2.0"
          memory: 4G
        reservations:
          cpus: "1.5"
          memory: 3G

networks:
  default:
    external: true
    name: locust-backend

# networks:
#   timescale-net: null

volumes:
  timescale_data: null