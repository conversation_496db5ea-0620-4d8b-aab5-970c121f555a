# -*- mode: python ; coding: utf-8 -*-
from PyInstaller.utils.hooks import get_package_paths

pyecharts_path = get_package_paths('pyecharts')[0]

a = Analysis(
    ['neox.py'],
    pathex=[],
    binaries=[('.\\bin\\SetDpi.exe', '.\\bin')],
    datas=[('.\\templates\\template_config.toml', '.\\templates')],
    hiddenimports=['common', 'scope', 'pyecharts'],
    hookspath=['.'],
    hooksconfig={},
    runtime_hooks=[],
    excludes=[],
    noarchive=False,
    optimize=0,
)
pyz = PYZ(a.pure)

exe = EXE(
    pyz,
    a.scripts,
    [],
    exclude_binaries=True,
    name='neox',
    debug=False,
    bootloader_ignore_signals=False,
    strip=False,
    upx=True,
    console=True,
    disable_windowed_traceback=False,
    argv_emulation=False,
    target_arch=None,
    codesign_identity=None,
    entitlements_file=None,
)
coll = COLLECT(
    exe,
    a.binaries,
    a.datas,
    strip=False,
    upx=True,
    upx_exclude=[],
    name='neox',
)
