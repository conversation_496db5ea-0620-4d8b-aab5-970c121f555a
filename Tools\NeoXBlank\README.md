# NeoXBlank

一个用于检查处方是否为空白的 HTTP 服务器。

# 安装配置

```bash
cd testing/Tools/NeoXBlank

# 配置 Python 环境
pip install -r requirements.txt
```

# 使用方法

## 运行服务器

```bash
python figure.py

# 或者后台执行
nohup python path/to/figure.py > output.log 2>&1 &
```

## 检查空白

### 请求

```bash
curl -X POST http://localhost:6999/check_blank -H "Content-Type: application/json" -d '{"image_path": "path/to/image.png"}'
```

### 响应

```json
{
    "result": 0.5
}
```
