[build-system]
requires = ["hatchling"]
build-backend = "hatchling.build"

[project]
name = "neox-test-common"
version = "0.0.1"
description = "The development kit of NeoX for automation testing (Common Lib)"
requires-python = ">=3.10"
dependencies = [
    "loguru>=0.7.3",
    "pendulum>=3.1.0",
    "PyYAML>=6.0.2",
    "toml>=0.10.2",
]
authors = [{ name = "Kuno Lu", email = "<EMAIL>" }]
readme = "README.md"
license = { text = "MIT License" }
classifiers = [
    "Development Status :: 4 - Beta",
    "Topic :: Software Development :: Libraries :: Python Modules",
    "Programming Language :: Python :: 3.11",
    "Programming Language :: Python :: 3.12",
    "Programming Language :: Python :: 3.13",
    "Operating System :: OS Independent",
]

[project.urls]
Repository = "https://bitbucket.org/neoxinc/testing/src/main/"
