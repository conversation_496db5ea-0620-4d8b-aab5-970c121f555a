# Changelog

All notable changes to this project will be documented in this file.

The format is based on [Keep a Changelog](https://keepachangelog.com/en/1.0.0/),
and this project adheres to [Semantic Versioning](https://semver.org/spec/v2.0.0.html).

## [Unreleased]

### Added
- Comprehensive documentation with usage examples
- Type hints for better IDE support and code clarity
- Detailed docstrings following Google style guidelines

### Changed
- Code formatting standardized across all modules
- Improved error handling with specific exception types
- Enhanced logging configuration with better defaults

## [0.1.0] - 2025-01-25

### Added
- **Core Framework Modules**
  - `TaskFrame`: HTTP request handling framework with JSON/string response parsing, file uploads, and assertion capabilities
  - `WSFrame`: WebSocket communication framework with real-time messaging and heartbeat support  
  - `MongoDB`: Database performance testing framework with automatic event tracking

- **Utility Function Modules**
  - `func_conf`: Configuration file handling for TOML, YAML, JSON, and CONF formats
  - `func_log`: Logging configuration and management with file rotation and custom formatting
  - `func_param`: Parameter processing, data manipulation, and stage management utilities

- **Parameter Management Modules**
  - `DQueue`: Data queue management for CSV-based parameterized testing
  - `ExprJmesMap`: JMESPath expression mapping for configuration access and path management
  - `GlobalContext`: Cross-module global variable management with thread-safe operations
  - `GlobalVariable`: Dynamic global variable utilities with attribute management

### Features

#### TaskFrame (HTTP Testing)
- Multiple response parsing modes (JSON, string, binary)
- File upload support with multipart/form-data
- Custom assertion functions for response validation
- Comprehensive logging with request/response details
- Automatic Locust event firing for performance metrics
- Support for custom headers and request bodies

#### WSFrame (WebSocket Testing)  
- Real-time message sending and receiving
- Heartbeat message simulation during idle periods
- Custom logging handlers with log rotation
- Automatic connection management
- Event tracking for performance analysis
- Support for custom message formats

#### MongoDB (Database Testing)
- Support for find, insert_one, and insert_many operations
- Automatic connection and database management
- Error handling with proper exception propagation
- Performance metrics collection
- Connection cleanup on test completion

#### Configuration Management
- **TOML**: Native support for TOML configuration files
- **YAML**: Full YAML parsing with safe loading
- **JSON**: High-performance JSON handling with orjson
- **CONF**: Simple key=value configuration file support
- Bidirectional conversion between formats

#### Parameter Processing
- UUID generation for unique test data
- String formatting with regular expression replacement
- CSV file processing for test data queues
- Custom iteration timing (constant and random intervals)
- Load test stage formatting for custom shapes
- Parameter mapping with method evaluation

#### Global Variable Management
- **GlobalContext**: Simple key-value storage across modules
- **GlobalVariable**: Dynamic attribute creation and management
- Thread-safe operations for concurrent testing
- Batch operations for efficient variable handling

### Technical Improvements
- **Type Safety**: Comprehensive type hints throughout the codebase
- **Documentation**: Detailed docstrings with examples and parameter descriptions
- **Error Handling**: Specific exception types with clear error messages
- **Code Quality**: Consistent formatting and structure across all modules
- **Performance**: Optimized operations with proper resource management

### Dependencies
- `locust`: Core testing framework integration
- `loguru`: Advanced logging capabilities
- `jmespath`: JSON/dictionary path expressions
- `orjson`: High-performance JSON processing
- `toml`: TOML configuration file support
- `pyyaml`: YAML configuration file support
- `pymongo`: MongoDB database connectivity
- `websocket-client`: WebSocket protocol support
- `gevent`: Asynchronous I/O for WebSocket operations

### Examples Added
- HTTP API testing with assertion validation
- WebSocket real-time communication testing
- MongoDB database operation benchmarking
- Configuration file management workflows
- Parameter-driven test data management
- Global variable sharing between test modules

### Documentation
- Comprehensive README with installation and usage guides
- Module-level documentation with feature explanations
- Code examples for common use cases
- Advanced usage patterns and best practices
- Contributing guidelines and development setup

---

## Version History Summary

- **v0.1.0**: Initial release with comprehensive Locust testing utilities
  - Core frameworks for HTTP, WebSocket, and MongoDB testing
  - Configuration management for multiple file formats
  - Parameter processing and global variable management
  - Production-ready logging and error handling

---

## Migration Guide

This is the initial release, so no migration is required.

## Support

For questions, issues, or feature requests:
- **Address**: [Bitbucket](https://bitbucket.org/neoxinc/testing/src/main/)
- **Documentation**: See README.md for detailed usage instructions

## Contributors

- KunoLu - Initial development and architecture

---

**Note**: This changelog follows [Keep a Changelog](https://keepachangelog.com/) format for easy tracking of project evolution.