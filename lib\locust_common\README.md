# Locust Common

[![Python Version](https://img.shields.io/badge/python-3.10%2B-blue.svg)](https://python.org)
[![License](https://img.shields.io/badge/license-MIT-green.svg)](LICENSE)

A comprehensive utility library for Locust performance testing, providing common frameworks, utilities, and tools to streamline load testing development.

## Features

### 🚀 Core Frameworks
- **HTTP Request Framework**: Comprehensive HTTP testing with JSON/string response parsing, file uploads, and assertion capabilities
- **WebSocket Framework**: Real-time WebSocket communication with event tracking and heartbeat support
- **MongoDB Framework**: Database performance testing with automatic event tracking

### 🛠️ Utility Functions
- **Configuration Management**: Support for TOML, YAML, JSON, and CONF file formats
- **Logging Setup**: Structured logging with rotation, retention, and custom formatting
- **Parameter Processing**: Advanced parameterization, data transformation, and stage management

### 📊 Parameter Management
- **Data Queues**: CSV-based parameter file processing for test data management
- **Global Variables**: Cross-module variable management with dynamic attribute support
- **Configuration Mapping**: JMESPath-based configuration access and path management

## Installation

```bash
pip install locust-common
```

Or install from source:

```bash
<NAME_EMAIL>:neoxinc/testing.git
cd lib/locust_common
pip install -e .
```

## Quick Start

### HTTP Testing Example

```python
from locust import HttpUser, task
from locust_common.frame.TaskFrame import FuncFrame
from loguru import logger

class MyUser(HttpUser):
    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        
    @task
    def test_api(self):
        # Create HTTP request framework
        request_frame = FuncFrame(
            method="POST",
            name="API Test",
            url="/api/users",
            headers={"Content-Type": "application/json"},
            body={"username": "test", "email": "<EMAIL>"},
            assert_keys=["id", "username"],  # Validate response contains these keys
            locust_logger=logger,
            log_flag=True
        )
        
        # Execute request with JSON response parsing
        response = request_frame.task_assert_resp_by_json(self)
```

### WebSocket Testing Example

```python
from locust_common.frame.WSFrame import WebSocketUser
from loguru import logger

class WSUser(WebSocketUser):
    def on_start(self):
        self.client.connect("ws://localhost:8080/ws")
        
    @task
    def send_message(self):
        self.client.send(
            body='{"type": "ping", "data": "hello"}',
            locust_logger=logger,
            log_enable=True
        )
        
    @task  
    def heartbeat_test(self):
        self.client.sleep_with_heartbeat(
            body='{"type": "heartbeat"}',
            seconds=30,
            locust_logger=logger
        )
```

### MongoDB Testing Example

```python
from locust_common.frame.MongoDB import MongoDBUser

class DBUser(MongoDBUser):
    conn_string = "mongodb://localhost:27017"
    db_name = "test_db"
    
    @task
    def test_database(self):
        # Test find operation
        self.client.execute_query(
            collection_name="users",
            operation="find",
            query={"status": "active"}
        )
        
        # Test insert operation
        self.client.execute_query(
            collection_name="users", 
            operation="insert_one",
            query={"name": "John", "email": "<EMAIL>"}
        )
```

### Configuration Management

```python
from locust_common.function.func_conf import load_toml, dump_json_file
from locust_common.param.ExprJmesMap import ExprJmespathMapping, get_conf
from pathlib import Path

# Load configuration from different formats
config = load_toml(Path("config.toml"))

# Use JMESPath mapping for configuration access
log_flag = get_conf(ExprJmespathMapping.LOCUST_LOG_FLAG)

# Convert and save configuration
dump_json_file(Path("output.json"), config)
```

### Parameter Management

```python
from locust_common.param.DQueue import data_queue
from locust_common.param.GlobalContext import add_kv, get_value
from locust_common.param.GlobalVariable import globalVariables as gbv
from pathlib import Path

# Create data queue from CSV file
param_queue = data_queue(Path("test_data.csv"))
user_data = param_queue.get_nowait()

# Global context management
add_kv("base_url", "https://api.example.com")
base_url = get_value("base_url")

# Dynamic global variables
gbv.api_key = "secret-key-123"
gbv.timeout = 30
print(gbv.all())  # List all variables
```

## Module Documentation

### Frame Module (`locust_common.frame`)

#### TaskFrame
- **Purpose**: HTTP request handling with comprehensive logging and assertion
- **Key Features**: JSON/string response parsing, file uploads, custom assertions
- **Use Case**: REST API testing, form submissions, file upload testing

#### WSFrame  
- **Purpose**: WebSocket communication framework
- **Key Features**: Real-time messaging, heartbeat support, event tracking
- **Use Case**: Real-time applications, chat systems, live data feeds

#### MongoDB
- **Purpose**: Database performance testing
- **Key Features**: Connection management, operation tracking, error handling
- **Use Case**: Database load testing, CRUD operation benchmarking

### Function Module (`locust_common.function`)

#### func_conf
- **Purpose**: Configuration file handling
- **Supported Formats**: TOML, YAML, JSON, CONF
- **Use Case**: Test configuration management, environment setup

#### func_log
- **Purpose**: Logging configuration and management
- **Key Features**: File rotation, custom formatting, level control
- **Use Case**: Test execution logging, debug information capture

#### func_param
- **Purpose**: Parameter processing and data manipulation
- **Key Features**: String formatting, stage management, iteration timing
- **Use Case**: Test data preparation, load test scenario configuration

### Parameter Module (`locust_common.param`)

#### DQueue
- **Purpose**: Data queue management for parameterized testing
- **Key Features**: CSV file processing, queue-based data distribution
- **Use Case**: User credential rotation, test data cycling

#### ExprJmesMap
- **Purpose**: JMESPath expression mapping for configuration access
- **Key Features**: Configuration value extraction, path management
- **Use Case**: Complex configuration navigation, conditional test execution

#### GlobalContext
- **Purpose**: Cross-module global variable management
- **Key Features**: Thread-safe operations, key-value storage
- **Use Case**: Sharing data between test modules, maintaining test state

#### GlobalVariable
- **Purpose**: Dynamic global variable utilities
- **Key Features**: Dynamic attribute creation, batch operations
- **Use Case**: Runtime configuration, flexible variable management

## Advanced Usage

### Custom Load Test Shapes

```python
from locust_common.function.func_param import fmt_stages

# Define load test stages
stages_config = [
    {"time": 60, "users": 10, "spawn_rate": 2},
    {"time": 120, "users": 50, "spawn_rate": 5},
    {"time": 180, "users": 100, "spawn_rate": 10}
]

formatted_stages = fmt_stages(stages_config)
```

### Custom Iteration Timing

```python
from locust_common.function.func_param import custom_iter_time

# Constant timing
constant_config = {
    "iter_time_fmt": "constant",
    "const_iter_time": 2
}

# Random timing
random_config = {
    "iter_time_fmt": "between", 
    "between_iter_time": [1.0, 3.0],
    "digits": 2
}

wait_time = custom_iter_time(random_config)
```

### File Upload Testing

```python
from pathlib import Path

# File upload with custom assertion
def validate_upload_response(response):
    return response.status_code == 200 and "file_id" in response.json()

response = request_frame.task_assert_resp_with_file_upload(
    self,
    file_path=Path("test_file.pdf"),
    file_field_name="document",
    content_type="application/pdf",
    form_data={"description": "Test document"},
    custom_assert_func=validate_upload_response
)
```

## Contributing

1. Fork the repository
2. Create a feature branch (`git checkout -b feature/amazing-feature`)
3. Commit your changes (`git commit -m 'Add amazing feature'`)
4. Push to the branch (`git push origin feature/amazing-feature`)
5. Open a Pull Request

### Development Setup

```bash
<NAME_EMAIL>:neoxinc/testing.git
cd lib/locust_common
pip install -e ".[dev]"
pre-commit install
```

### Running Tests

```bash
pytest tests/
```

### Code Style

This project uses:
- **Black** for code formatting
- **isort** for import sorting  
- **flake8** for linting
- **mypy** for type checking

```bash
black locust_common/
isort locust_common/
flake8 locust_common/
mypy locust_common/
```

## Requirements

- Python 3.10+
- Locust 2.0+
- Dependencies: `loguru`, `jmespath`, `orjson`, `toml`, `yaml`, `pymongo`, `websocket-client`, `gevent`

## License

This project is licensed under the MIT License.

## Support

- **Address**: [Bitbucket](https://bitbucket.org/neoxinc/testing/src/main/)

## Changelog

See [CHANGELOG.md](CHANGELOG.md) for a detailed history of changes.
