#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Common Utilities Module

This module provides common utilities for the NeoX testing framework,
including configuration file handling, UI automation, and global context management.

@Date       :   2024-02-11
<AUTHOR>   KunoLu
@Email      :   <EMAIL>
@File       :   __init__.py
@Software   :   PyCharm
"""

# Import configuration utilities
from .common import (
    ProjectConfig,
    dump_json_file,
    dump_toml,
    dump_yml,
    load_json_file,
    load_toml,
    load_yml,
)

# Import UI automation utilities
from .UIA import UIA

# Import global context utilities (if available)
try:
    from .GlobalContext import GlobalContext

    _global_context_available = True
except ImportError:
    # GlobalContext module may not be available in all environments
    _global_context_available = False

# Define public API
__all__ = [
    # Configuration utilities
    "ProjectConfig",
    "load_toml",
    "load_yml",
    "load_json_file",
    "dump_toml",
    "dump_yml",
    "dump_json_file",
    # UI Automation utilities
    "UIA",
]

# Add GlobalContext to __all__ if available
if _global_context_available:
    __all__.append("GlobalContext")
