# -*- coding: utf-8 -*-
"""
@Time     :   2025/05/09 15:11:39
<AUTHOR>   <PERSON><PERSON><PERSON><PERSON> 
@Email    :   <EMAIL>
@File     :   global_context.py
@Software :   Cursor
"""

from aenum import extend_enum
from locust_common.param.ExprJmesMap import ExprJmespathMapping as ejm


class ExtendEJM:

    def __init__(self, common_ejm):
        self._common_ejm = common_ejm

    @property
    def extend_ejm(self) -> ejm:
        # global
        extend_enum(self._common_ejm, "ENV_FILE", "global.ENV_FILE")

        # task.enquete
        extend_enum(self._common_ejm, "ENQUETE_TASK_WEIGHT",
                    "task.enquete.task_weight")

        # shape.stages
        extend_enum(self._common_ejm, "STAGES", "shape.stages")

        # iteration.interrupt
        extend_enum(self._common_ejm, "INTERRUPT", "iteration.interrupt")

        # iteration.enquete
        extend_enum(self._common_ejm, "ENQUETE_ITER_TIME_CONF",
                    "iteration.enquete")

        # param_files.enquete
        extend_enum(self._common_ejm, "PARAM_STRATEGY_ENQUETE",
                    "param_files.enquete.param_data_loop_enable")
        extend_enum(self._common_ejm, "PARAM_FILE_ENQUETE",
                    "param_files.enquete.base_data_filename")

        # param.mongo.operation_type
        extend_enum(self._common_ejm, "MONGO_OPER_TYPE",
                    "param.mongo.operation_type")

        # log
        extend_enum(self._common_ejm, "INTERNAL_LOG_FLAG",
                    "log.internal_log_flag")

        return self._common_ejm
