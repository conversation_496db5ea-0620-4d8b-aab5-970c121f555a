{"toml": {"global": {"ENV_FILE": "BackendDBConfig.env"}, "task": {"prescriptions": {"task_weight": 1}}, "shape": {"stages": [{"time": 60, "users": 5, "spawn_rate": 1}, {"time": 60, "users": 5, "spawn_rate": 1}, {"time": 60, "users": 5, "spawn_rate": 1}, {"time": 60, "users": 5, "spawn_rate": 1}, {"time": 60, "users": 5, "spawn_rate": 1}]}, "iteration": {"interrupt": false, "prescriptions": {"iter_time_fmt": "constant", "const_iter_time": 3, "between_iter_time": [10.0, 20.0]}}, "param_files": {"prescriptions": {"param_data_loop_enable": true, "base_data_filename": "param_data_pres.csv"}}, "param": {"mongo": {"operation_type": "insert_one"}}, "log": {"internal_log_flag": true}}, "conf": {"locustfile": "scene_kenta_prescriptions.py", "exit-code-on-error": "1", "headless": "true", "csv": "result/scene_kenta_prescriptions", "csv-full-history": "true", "html": "result/scene_kenta_prescriptions.html", "print-stats": "true", "only-summary": "true", "logfile": "run.log", "loglevel": "INFO", "reset-stats": "true", "stop-timeout": "5"}}