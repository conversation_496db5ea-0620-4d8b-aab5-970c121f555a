#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
@Date       :   2021-08-17
<AUTHOR>   Ku<PERSON><PERSON><PERSON>
@Email      :   <EMAIL>
@File       :   demo_execute_sequence
@Software   :   PyCharm
"""

from locust import HttpUser, TaskSet, task, between

'''
执行顺序：
Locust setup → TaskSet setup → TaskSet on_start → 
TaskSet tasks → TaskSet on_stop → TaskSet teardown → 
Locust teardown
'''


class UserBehavor(TaskSet):
    # 启动locust是运行setup方法
    def setup(self):
        print('task setup')

    def teardown(self):
        print('task teardown')

    # 虚拟用户启动task时运行
    def on_start(self):
        print('start')

    # 虚拟用户结束task时运行
    def on_stop(self):
        print('end')

    @task(2)
    def index(self):
        self.client.get('/')

    @task(1)
    def profile(self):
        self.client.get('/profile')


class WebsitUser(HttpUser):
    def setup(self):
        print('locust setup')

    def teardown(self):
        print('locust teardown')

    host = 'http://xxx.com'
    task_set = task(UserBehavor)
    wait_time = between(1, 3)


if __name__ == '__main__':
    pass
