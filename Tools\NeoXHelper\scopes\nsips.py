# -*- coding: utf-8 -*-
"""
@Date       :   2024/8/23
<AUTHOR>   <PERSON><PERSON>Lu
@Email      :   <EMAIL>
@File       :   nsips.py
@Software   :   PyCharm
"""
import re
import petl as etl
from pathlib import Path
from decimal import Decimal
from typing import List, Tuple, Dict

from pyecharts import options as opts
from pyecharts.charts import Line, Grid
from pyecharts.globals import ThemeType


class NSIPSETL:

    def __init__(self, **kwargs: Dict[str, str or int] or None) -> None:
        """
        Initialize NSIPSETL.
        :param kwargs: Dict[str, str or int] or None
        """
        flag = kwargs is not None
        self.pattern = kwargs.get('pattern') if flag and 'pattern' in kwargs.keys() else r"\d{4}-\d{2}-\d{2}"
        self.tar_sheet_name = kwargs.get('tar_sheet_name') if flag and 'tar_sheet_name' in kwargs.keys() else '概要-当日'
        self.ori_store_title = kwargs.get('ori_store_title') if flag and 'ori_store_title' in kwargs.keys() else '店舗名'
        self.ori_cons_title = kwargs.get('ori_cons_title') if flag and 'ori_cons_title' in kwargs.keys() else '一致率'
        self.sum_cell_value = kwargs.get('sum_cell_value') if flag and 'sum_cell_value' in kwargs.keys() else '全体'
        self.decimal = kwargs.get('decimal') if flag and 'decimal' in kwargs.keys() else 4

    def extract_date_from_filename(self, filename: str) -> str or None:
        """
        Extract date from filename.
        :param filename: str
        :return: str or None
        """
        match = re.search(self.pattern, filename)
        if match:
            date = match.group()
            return date
        else:
            return None

    def parse_nsips_table(self, input_file: Path) -> (bool, Tuple[etl.Table, str] or None):
        """
        Parse NSIPS table.
        :param input_file: Path
        :return: bool, Tuple[Table, str] or None
        """
        suffix = input_file.suffix.split('.')[1]
        if suffix != 'xlsx':
            return False, None

        res_title_name = self.extract_date_from_filename(input_file.stem)
        self.decimal = 4

        nspis_table = etl.fromxlsx(filename=input_file, sheet=self.tar_sheet_name)

        nspis_tbl_cons = etl.cut(nspis_table, self.ori_store_title, self.ori_cons_title)
        nsips_tbl_each_cons = etl.sort(table=etl.convert(
            etl.select(nspis_tbl_cons, "{%s} != '%s'" % (self.ori_store_title, self.sum_cell_value)),
            self.ori_cons_title, lambda v: round(Decimal(v.strip('%')), self.decimal)),
            key=self.ori_cons_title)

        nsips_tbl_sum_cons = etl.convert(etl.tail(nspis_tbl_cons, 1),
                                         self.ori_cons_title,
                                         lambda v: round(Decimal(v.strip('%')), self.decimal))

        parsed_nsips_table = etl.rename(etl.stack(nsips_tbl_each_cons, nsips_tbl_sum_cons),
                                        self.ori_cons_title, res_title_name)

        parsed_data = (parsed_nsips_table, nsips_tbl_each_cons, nsips_tbl_sum_cons, res_title_name)

        return True, parsed_data if parsed_nsips_table else False, None

    def aggregate_parsed_nsips_table(self,
                                     nsips_tables: List[etl.Table] or None,
                                     join_key: str or None = None) -> etl.Table or None:
        """
        Aggregate parsed NSIPS table.
        :param nsips_tables: List[Table] or None
        :param join_key: str or None
        :return: Table or None
        """

        if nsips_tables is None:
            return None
        if len(nsips_tables) == 1:
            return nsips_tables[0]

        sum_nsips_table = None
        for idx, table in enumerate(nsips_tables):
            if idx == 0:
                sum_nsips_table = table
            else:
                sum_nsips_table = etl.outerjoin(sum_nsips_table,
                                                table,
                                                key=self.ori_store_title if join_key is None else join_key)

        return sum_nsips_table

    def move_sum_row_to_bottom(self, table: etl.Table, title: str or None = None, content: any = None) -> etl.Table:
        """
        Move sum row to bottom.
        :param table: Table
        :param title: str or None
        :param content: any
        :return: Table
        """
        tar_title = self.ori_store_title if title is None else title
        sum_content = self.sum_cell_value if content is None else content
        table_each, table_sum = etl.biselect(table, "{%s} != '%s'" % (tar_title, sum_content))
        return etl.stack(table_each, table_sum)


class NSIPSCharts:

    def __init__(self,
                 source: str or Path,
                 nsips_date: list,
                 res_path: Path,
                 src_sheet_name: str = 'Trend',
                 tar_sum_name: str = '全体',
                 store_title: str = '店舗名') -> None:
        """
        Initialize NSIPSCharts.
        :param source: str or Path
        :param nsips_date: list
        :param res_path: Path
        :param src_sheet_name: str
        :param tar_sum_name: str
        :param store_title: str
        """
        src_file = source if isinstance(source, Path) else Path(source)
        self.charts_data = etl.fromxlsx(filename=src_file, sheet=src_sheet_name)
        self.nsips_date = nsips_date
        self.sum_name = tar_sum_name
        self.store_title = store_title
        self.res_path = res_path

    def _chart_line_each(self, html_path: Path, html_name: str = 'NSIPS突合：各店铺一致率趋势图') -> None:
        """
        Create single line chart for each store in the NSIPS data.
        :param html_path: Path
        :param html_name: str
        :return: None
        """
        line_each = Line(init_opts=opts.InitOpts(theme=ThemeType.MACARONS))

        # 添加图表 x、y轴数据
        line_each.add_xaxis(xaxis_data=self.nsips_date)
        for row in etl.dicts(self.charts_data):
            store = row[self.store_title]
            value_each = []
            for date_key in self.nsips_date:
                value_each.append(row[date_key])
            if store != self.sum_name:
                line_each.add_yaxis(series_name=store, y_axis=value_each,
                                    is_smooth=True, is_connect_nones=True, label_opts=opts.LabelOpts(is_show=False))

        # 设置标记线类型（平均值）
        line_each.set_series_opts(
            markline_opts=opts.MarkLineOpts(
                data=[
                    opts.MarkLineItem(name="平均值", type_="average")
                ],
            )
        )

        # 设置图表全局选项
        line_each.set_global_opts(
            title_opts=opts.TitleOpts(title=html_name, pos_left="center"),
            axispointer_opts=opts.AxisPointerOpts(is_show=True, link=[{"xAxisIndex": "all"}]),
            xaxis_opts=opts.AxisOpts(type_="category", boundary_gap=False),
            yaxis_opts=opts.AxisOpts(max_=100, name="一致率(%)"),
            toolbox_opts=opts.ToolboxOpts(is_show=True, pos_left="70%"),
            tooltip_opts=opts.TooltipOpts(trigger="axis"),
            legend_opts=opts.LegendOpts(type_="scroll", selected_mode="multiple", orient="vertical",
                                        pos_left="right", pos_top="5%",
                                        selector=True, is_page_animation=True),
            datazoom_opts=[
                opts.DataZoomOpts(
                    is_show=True,
                    is_realtime=True,
                    range_start=0,
                    range_end=100,
                )
            ],
        )

        # 设置并行多图并生成网页图表
        html_filepath = html_path.joinpath(html_name + '.html')
        (
            Grid(init_opts=opts.InitOpts(width="1280px", height="1024px"))
            .add(chart=line_each, grid_opts=opts.GridOpts(pos_left=50, pos_right=300))
            .render(html_filepath.as_posix())
        )

    def _chart_line_sum(self, html_path: Path, html_name: str = 'NSIPS突合：总一致率趋势图') -> None:
        """
        Create line chart for the sum of stores in the NSIPS data.
        :param html_path: Path
        :param html_name: str
        :return: None
        """
        line_sum = Line(init_opts=opts.InitOpts(theme=ThemeType.MACARONS))

        # 添加图表 x、y轴数据
        line_sum.add_xaxis(xaxis_data=self.nsips_date)
        sum_data = etl.tail(self.charts_data, 1)
        value_sum = []
        for date_key in self.nsips_date:
            value_sum.append(list(etl.values(sum_data, date_key))[0])
        line_sum.add_yaxis(
            series_name=list(etl.values(sum_data, self.store_title))[0],
            y_axis=value_sum,
            is_smooth=True,
            is_connect_nones=True
        )

        # 设置标记线类型（最大值、最小值、平均值）
        line_sum.set_series_opts(
            markpoint_opts=opts.MarkPointOpts(
                data=[
                    opts.MarkPointItem(name="最大值", type_="max"),
                    opts.MarkPointItem(name="最小值", type_="min"),
                ],
                symbol="arrow",
                symbol_size=30
            ),
            markline_opts=opts.MarkLineOpts(
                data=[
                    opts.MarkLineItem(name="平均值", type_="average")
                ],
            )
        )

        # 设置图表全局选项
        line_sum.set_global_opts(
            title_opts=opts.TitleOpts(title=html_name, pos_left="center"),
            axispointer_opts=opts.AxisPointerOpts(is_show=True, link=[{"xAxisIndex": "all"}]),
            xaxis_opts=opts.AxisOpts(type_="category", boundary_gap=False),
            yaxis_opts=opts.AxisOpts(max_=100, name="一致率(%)"),
            toolbox_opts=opts.ToolboxOpts(is_show=True, pos_left="70%"),
            tooltip_opts=opts.TooltipOpts(trigger="axis"),
            legend_opts=opts.LegendOpts(type_="plain", selected_mode="single", orient="vertical",
                                        pos_left="right", pos_top="55%"),
            datazoom_opts=[
                opts.DataZoomOpts(
                    is_show=True,
                    is_realtime=True,
                    range_start=0,
                    range_end=100,
                )
            ],
        )

        # 设置并行多图并生成网页图表
        html_filepath = html_path.joinpath(html_name + '.html')
        (
            Grid(init_opts=opts.InitOpts(width="1280px", height="1024px"))
            .add(chart=line_sum, grid_opts=opts.GridOpts(pos_left=50, pos_right=100))
            .render(html_filepath.as_posix())
        )

    def gen_chart_line(self) -> None:
        """
        Generate line chart for each store and the sum of stores in the NSIPS data.
        :return: None
        """
        self._chart_line_each(html_path=self.res_path)
        self._chart_line_sum(html_path=self.res_path)
