# -*- coding: utf-8 -*-
"""
@Date       :   2024-02-01
<AUTHOR>   <PERSON><PERSON>Lu
@Email      :   <EMAIL>
@File       :   configuration.py
@Software   :   PyCharm
"""
import toml
import time
from pathlib import Path
from rich.console import Console
from typing import MutableMapping, Any

console = Console()


def load_toml(config_file: Path) -> MutableMapping[str, Any]:
    """
    读取 toml 配置文件
    :param config_file: toml 配置文件
    :return: 字典格式的配置文件内容
    """
    # 打开读取 toml 配置文件
    with open(config_file, 'r', encoding='utf-8') as f:
        config = toml.load(f)
    return config


def console_log(std_outs: str, std_errs: str | bytes | None) -> None:
    """
    响应内容解析
    :param std_outs: response in stdout
    :param std_errs: response in stderr
    """
    if std_errs is not None:
        std_err = std_errs.decode('utf-8') if isinstance(std_errs, bytes) else std_errs
        console.log(
            f"StdErr is: [ {std_err} ]",
            style="bold red",
            log_locals=True
        )
    else:
        console.log(
            f"StdOut is: [ {std_outs} ]",
            style="bold green",
            log_locals=False
        )


def timer(msg=None, log_func=print):
    """
    A context manager that measures the time taken by a code block.

    Args:
        msg (str, optional): A message to be printed at the end of the code block.
            Defaults to None.
        log_func (function, optional): A function to be used for printing the time
            taken by the code block. Defaults to print.

    Yields:
        None

    Returns:
        None

    Examples:
        >>> with timer("This block took"):
        ...     time.sleep(1)
        This block took | 1 sec
    """
    begin_time = time.perf_counter()
    yield
    time_elapsed = time.perf_counter() - begin_time
    log_func(f"{msg or 'timer'} | {int(time_elapsed)} sec")
