# -*- coding: utf-8 -*-
"""
@Date       :   2024-02-12
<AUTHOR>   <PERSON><PERSON>Lu
@Email      :   <EMAIL>
@File       :   login_success.py
@Software   :   PyCharm
"""
import allure
from jmespath import search
from pytest_bdd import given, when, then

from neox_test_common import logger, UIA
from neox_test_scenarios import com_show_desktop, com_open_yakushi_app, com_write_acc_info, com_click_login_btn


@given("显示桌面")
def show_desktop():
    with allure.step("显示桌面"):
        com_show_desktop()


@when("打开Yakushi客户端")
def open_yakushi_app(config):
    with allure.step("打开Yakushi客户端"):
        assert com_open_yakushi_app(config)


@when("在账户信息文本框中输入正确的账户名")
def write_account_name(config):
    with allure.step("在账户信息文本框中输入正确的账户名"):
        user_data = {
            "control": search("yakushi.modules.login.control.box.user", config),
            "log": {
                "debug": "账户信息文本框窗口",
                "info": "< Step 1 :: 在账户信息文本框中输入正确的账户名 >"
            },
            "text": search("yakushi.modules.login.account", config)
        }

        assert com_write_acc_info(user_data)


@when("在账户密码文本框中输入正确的密码")
def write_account_pwd(config):
    with allure.step("在账户密码文本框中输入正确的密码"):
        pwd_data = {
            "control": search("yakushi.modules.login.control.box.password", config),
            "log": {
                "debug": "账户密码文本框窗口",
                "info": "< Step 2 :: 在账户密码文本框中输入正确的密码 >"
            },
            "text": search("yakushi.modules.login.password", config)
        }

        assert com_write_acc_info(pwd_data)


@when("点击登录按钮")
def click_login_button(config):
    with allure.step("点击登录按钮"):
        btn_data = {
            "control": search("yakushi.modules.login.control.btn.login", config),
            "log": {
                "debug": "登录按钮",
                "info": "< Step 3 :: 点击登录按钮 >"
            }
        }

        assert com_click_login_btn(btn_data)


@then("登录成功并在跳转后的页面中找到断言标志")
def check_login_status():
    with allure.step("登录成功并在跳转后的页面中找到断言标志"):
        logger.info("< Step 4 :: 验证是否为客户端安装后首次登录...检测中... >")
        cert_installed_window = UIA.WindowControl(Name='', ClassName='#32770', searchDepth=2, timeout=20)
        if cert_installed_window is not None:
            logger.info("< Step 4 :: 确认为客户端安装后首次登录 >")
            UIA.setWindowActive(cert_installed_window)
            cert_installed_confirm_button = UIA.ButtonControl(parent=cert_installed_window, Name='确定')
            _ = UIA.clickButton(button=cert_installed_confirm_button)
            logger.info("< Step 4 :: 点击证书安装完成弹窗的确认按钮 >")
            logger.debug(f"证书安装完成弹窗的确认按钮-RECT：{cert_installed_confirm_button.BoundingRectangle}")
        else:
            logger.info("< Step 4 :: 确认非客户端安装后首次登录，无需安装证书 >")

        float_window = UIA.WindowControl(Name='薬師丸賢太', ClassName='Window', AutomationId='FloatWindow', timeout=3)
        logger.debug(f"浮动窗口-RECT：{float_window.BoundingRectangle}")
        main_window = UIA.WindowControl(Name='薬師丸賢太', ClassName='Window', AutomationId='MainWindow', timeout=3)
        logger.debug(f"主页窗口-RECT：{main_window.BoundingRectangle}")

        assert all((float_window, main_window))
