# README

## Illustration

1. `conf/locust.conf` 为单台压力机/容器下，使用单进程模式执行测试脚本（只会使用单个 CPU 去发压）的配置文件；
2. `locust_master.conf` 为使用单台压力机/容器的多进程模式（每个 worker 使用 1 个进程） or 远程分布式模式下的 master 配置文件；
3. `locust_worker.conf` 为使用单台压力机/容器的多进程模式（每个 worker 使用 1 个进程） or 远程分布式模式下的 worker 配置文件。

## Tips

1. `单机多进程模式`一般是为了充分利用单台压力机的 CPU，故压力机是几 `C` 就启用多少个 worker。（不用管 master，主要消耗 CPU 的是 worker）
2. 使用`单机多进程模式`或`远程分布式模式`需要注意，每个 worker 均会独立使用参数化文件中的参数，所以参数化文件中的值并不是全局的，故：
   1. 在`单机多进程模式`中，很多场景下需要提前分配好参数，拆分给每个 worker 使用；
   2. 在`远程分布式模式`中，建议把参数化数据预埋到 `redis` 中，在测试脚本中从`redis`读取参数。