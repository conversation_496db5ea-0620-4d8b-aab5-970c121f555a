#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
@Date       :   2024-02-11
<AUTHOR>   KunoLu
@Email      :   <EMAIL>
@File       :   config.py
@Software   :   PyCharm
"""

from dataclasses import dataclass


@dataclass(frozen=True)
class YakushiFeaturesName:
    """
    Configuration class for Yakushi application feature file paths.

    This immutable dataclass stores the relative paths to various Gherkin
    feature files used in Yakushi application testing. Each attribute
    represents a specific functional area or test scenario within the
    Yakushi system.

    The class is frozen to prevent accidental modification of feature
    paths during test execution, ensuring consistency across test runs.

    Attributes:
        LOGIN_LOGOUT: Authentication feature file path for login/logout scenarios
        HOMEPAGE_RV: Homepage reviewed prescriptions list feature file
        HOMEPAGE_URV: Homepage unreviewed prescriptions list feature file
        PH_DETAIL: Prescription history detail view feature file
        PH_FAULT: Prescription history fault handling feature file
        PH_FILTER: Prescription history filtering feature file
        PH_PAGING: Prescription history pagination feature file
        NSIPS_CLEANUP: NSIPS comparison data cleanup feature file
        NSIPS_DETAIL: NSIPS comparison detail view feature file
        NSIPS_FILTER: NSIPS comparison filtering feature file
        NSIPS_PAGING: NSIPS comparison pagination feature file
        NSIPS_SORT: NSIPS comparison sorting feature file
        NSIPS_UPDATE: NSIPS comparison data update feature file
        FAULT_FP: Fault feedback process feature file
        SETTING_PATH: Settings input/output path configuration feature file
        SETTING_QR: Settings QR code configuration feature file
        SETTING_NOTIF: Settings PDF notification configuration feature file
        SETTING_INPUT: Settings input file configuration feature file
        UI_FW: UI floating window validation feature file
        UI_HPW: UI homepage window validation feature file
        UI_POPUP: UI popup window validation feature file

    Example:
        >>> features = YakushiFeaturesName()
        >>> print(features.LOGIN_LOGOUT)
        ./登录登出/authentication.feature

        >>> # Access through the global instance
        >>> print(FEATURES_NAME.HOMEPAGE_RV)
        ./首页/reviewed.feature

    Note:
        All paths are relative and use forward slashes for cross-platform
        compatibility. The paths include Chinese directory names that
        correspond to the functional areas in the Yakushi application.
    """

    # Authentication
    LOGIN_LOGOUT: str = "./登录登出/authentication.feature"
    # Homepage
    HOMEPAGE_RV: str = "./首页/reviewed.feature"
    HOMEPAGE_URV: str = "./首页/unreviewed.feature"
    # Prescription History
    PH_DETAIL: str = "./处方笺履历/detail.feature"
    PH_FAULT: str = "./处方笺履历/fault.feature"
    PH_FILTER: str = "./处方笺履历/filter.feature"
    PH_PAGING: str = "./处方笺履历/paging.feature"
    # NSIPS Comparison
    NSIPS_CLEANUP: str = "./NSIPS突合/cleanup.feature"
    NSIPS_DETAIL: str = "./NSIPS突合/detail.feature"
    NSIPS_FILTER: str = "./NSIPS突合/filter.feature"
    NSIPS_PAGING: str = "./NSIPS突合/paging.feature"
    NSIPS_SORT: str = "./NSIPS突合/sort.feature"
    NSIPS_UPDATE: str = "./NSIPS突合/update.feature"
    # Setting
    SETTING_PATH: str = "./设定/path.feature"
    SETTING_QR: str = "./设定/qr.feature"
    SETTING_NOTIF: str = "./设定/notification.feature"
    SETTING_INPUT: str = "./设定/input.feature"
    # UI
    UI_FW: str = "./UI/floating.feature"
    UI_HPW: str = "./UI/homepage.feature"
    UI_POPUP: str = "./UI/popup.feature"
    # Other (fault feedback process)
    FAULT_FP: str = "./其他/fault.feature"


FEATURES_NAME = YakushiFeaturesName()
