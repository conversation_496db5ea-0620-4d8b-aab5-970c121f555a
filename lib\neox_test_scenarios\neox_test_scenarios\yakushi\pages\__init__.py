#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Yakushi Pages Module

This module provides Page Object Model implementations for Yakushi application testing.
It includes base page classes and specific page implementations for different functional modules.

@Date       :   2024-02-19
<AUTHOR>   <PERSON><PERSON><PERSON><PERSON>
@Email      :   <EMAIL>
@File       :   __init__.py
@Software   :   PyCharm
"""

# Import base page classes
from .base.base_page import BasePage

# Import specific page implementations
try:
    from .login.login_page import LoginPage

    _LOGIN_PAGE_AVAILABLE = True
except ImportError:
    LoginPage = None
    _LOGIN_PAGE_AVAILABLE = False

try:
    from .main.main_page import MainPage

    _MAIN_PAGE_AVAILABLE = True
except ImportError:
    MainPage = None
    _MAIN_PAGE_AVAILABLE = False

try:
    from .prescription_history.prescription_history_page import PrescriptionHistoryPage

    _PRESCRIPTION_HISTORY_PAGE_AVAILABLE = True
except ImportError:
    PrescriptionHistoryPage = None
    _PRESCRIPTION_HISTORY_PAGE_AVAILABLE = False

try:
    from .nsips.nsips_page import NSIPSPage

    _NSIPS_PAGE_AVAILABLE = True
except ImportError:
    NSIPSPage = None
    _NSIPS_PAGE_AVAILABLE = False

try:
    from .settings.settings_page import SettingsPage

    _SETTINGS_PAGE_AVAILABLE = True
except ImportError:
    SettingsPage = None
    _SETTINGS_PAGE_AVAILABLE = False

try:
    from .homepage.homepage_page import HomePage

    _HOMEPAGE_PAGE_AVAILABLE = True
except ImportError:
    HomePage = None
    _HOMEPAGE_PAGE_AVAILABLE = False

try:
    from .ui.ui_page import UIPage

    _UI_PAGE_AVAILABLE = True
except ImportError:
    UIPage = None
    _UI_PAGE_AVAILABLE = False

try:
    from .other.other_page import OtherPage

    _OTHER_PAGE_AVAILABLE = True
except ImportError:
    OtherPage = None
    _OTHER_PAGE_AVAILABLE = False

# Define public API
__all__ = [
    "BasePage",
]

if _LOGIN_PAGE_AVAILABLE:
    __all__.append("LoginPage")
if _MAIN_PAGE_AVAILABLE:
    __all__.append("MainPage")
if _PRESCRIPTION_HISTORY_PAGE_AVAILABLE:
    __all__.append("PrescriptionHistoryPage")
if _NSIPS_PAGE_AVAILABLE:
    __all__.append("NSIPSPage")
if _SETTINGS_PAGE_AVAILABLE:
    __all__.append("SettingsPage")
if _HOMEPAGE_PAGE_AVAILABLE:
    __all__.append("HomePage")
if _UI_PAGE_AVAILABLE:
    __all__.append("UIPage")
if _OTHER_PAGE_AVAILABLE:
    __all__.append("OtherPage")
