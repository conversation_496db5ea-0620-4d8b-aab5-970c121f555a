# NeoXHelper

一个处理 Windows | Web | NeoX 生产环境需求的 CLI 工具。

# 安装配置

```bash
cd testing\Tools\NeoXHelper

# 使用 uv 设置 python 环境
# 如果您还没有安装 uv，请参考：https://github.com/astral-sh/uv
uv venv
# 激活虚拟环境
# Windows PowerShell: .\.venv\Scripts\Activate.ps1
# Linux/macOS: source .venv/bin/activate

# 安装依赖
uv pip install -e .[dev]

# 以下命令用于构建可执行二进制文件
# 运行以下命令前请先配置 neox.spec
pyinstaller neox.spec

# 此外，您也可以使用以下命令进行构建
# neox.spec 配置与以下命令相同（请更新 {your_env_path} ！！！）
pyinstaller --clean --onedir --console --add-data ".\\templates\\template_config.toml:.\\templates" --add-data "{your_env_path}\\Lib\\site-packages\\pyecharts:.\\pyecharts" --add-binary ".\\bin\\SetDpi.exe:.\\bin" --hidden-import common --hidden-import scope neox.py
```

# 使用方法

示例：

```context
示例 1:
    neox
示例 2:
    neox -- help

-> 说明:
    注意：格式是 '-- help' ！！！
    示例 1 == 示例 2。

示例 3:
    neox template generate --path=.
示例 4:
    neox demo generate --path=~/neox

-> 说明:
    生成配置模板到指定目录。
    模板文件名：
        1. template_config.toml
    注意：
        1. '--path' 可以执行绝对路径和相对路径；
        2. 使用 '--path' 时路径必须存在。

示例 5:
    neox nsips get --file=config.toml

-> 说明:
    根据配置文件中的设置生成图表和 xlsx 文件。
    结果文件将生成到 src_dir/result 目录中。

示例 6:
    neox win feed --file=./config.toml
示例 7:
    neox windows install --file=config.toml
示例 8:
    neox win uninstall --file=~/neox/config.toml
示例 9:
    neox win get dpi == neox win get 0
示例 10:
    neox windows get resolution == neox win get 1
示例 11:
    neox windows set --file=config.toml
示例 12:
    neox win mkdirs --file=config.toml

-> 说明:
    当您需要自定义配置文件时，'--file' 可以执行绝对路径和相对路径。
```
