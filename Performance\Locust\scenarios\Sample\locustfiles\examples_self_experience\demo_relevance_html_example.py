#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
@Date       :   2021-08-18
<AUTHOR>   KunoLu
@Email      :   <EMAIL>
@File       :   demo_relevance_html_example
@Software   :   PyCharm
"""

from locust import HttpUser, TaskSet, task, between
from lxml import etree
import json
import urllib3
import os

urllib3.disable_warnings()


class LoginTest(TaskSet):
    """
    定义用户行为类
    """

    def get_execution_lt(self):
        """
        获取登录的动态参数
        """
        url = '/passport/login'
        headers = {
            "User-Agent": "Mozilla/5.0 (Windows NT 10.0; WOW64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/81.0.4044.138 Safari/537.36"}
        self.client.headers.update(headers)
        r = self.client.get(url=url, verify=False)
        dom = etree.HTML(r.content.decode('utf-8'))  # 返回的是list对象
        result = {}
        try:
            # print(dom.xpath('//*[@id="fm1"]/input[2]')[0].get('value'))
            result['lt'] = dom.xpath('//input[@name="lt"]')[0].get('value')
            result['execution'] = dom.xpath('//input[@name="execution"]')[0].get('value')
            print(json.dumps(result, sort_keys=True, indent=10))
        except:
            print("动态参数lt、execution获取失败！")
        return result

    def login_action(self, account, password):
        # 登录
        login_setup = self.get_execution_lt()
        url2 = '/passport/login'
        body = {
            "username": account,
            "password": password,
            "lt": login_setup['lt'],
            "execution": login_setup['execution'],
            "_eventId": 'submit'
        }
        h2 = {
            "Content-Type": "application/x-www-form-urlencoded"

        }
        self.client.headers.update(h2)
        # import json,urllib.parse
        # data_dic = eval(body)
        # data_urlencode = urllib.parse.urlencode(data_dic).encode(encoding='UTF-8')

        r2 = self.client.post(url=url2, data=body, verify=False)
        try:
            assert '退出' in r2.text
            print("登录成功")
        except:
            print("登录失败")

    def on_start(self):
        account = '******'
        password = '****'
        self.login_action(account, password)

    @task(1)
    def get_account(self):
        # 登录后请求
        r3 = self.client.get(url='https://account.chsi.com.cn/account/account!show')
        try:
            assert '查看个人信息_学信网' in r3.text
            print("查询成功")
        except:
            print("查询失败")


class WebSiteUser(HttpUser):
    tasks = [LoginTest]
    host = "https://account.chsi.com.cn"
    wait_time = between(3, 6)


if __name__ == '__main__':
    os.system('locust -f locust_xuexin.py')
