import cv2
import urllib.request
import numpy as np
from sanic import Sanic, response

app = San<PERSON>("ImageBlankChecker")


def is_blank_page(image_path):
    # 如果image_path是URL，下载图片
    if image_path.startswith('http'):
        resp = urllib.request.urlopen(image_path)
        image = np.asarray(bytearray(resp.read()), dtype="uint8")
        image = cv2.imdecode(image, cv2.IMREAD_GRAYSCALE)
    else:
        image = cv2.imread(image_path, cv2.IMREAD_GRAYSCALE)

    image[image < 3] = 255
    _, image = cv2.threshold(image, 170, 255, cv2.THRESH_BINARY)
    kernel = cv2.getStructuringElement(cv2.MORPH_ELLIPSE, (25, 25))
    image = cv2.morphologyEx(image, cv2.MORPH_OPEN, kernel)
    mean_val = cv2.mean(image)[0]
    variance = cv2.mean(cv2.subtract(image, mean_val))[0]
    return variance


@app.route("/check_blank", methods=["POST"])
async def check_blank(request):
    image_path = request.json.get("image_path")
    result = is_blank_page(image_path)
    return response.json({"result": result})


if __name__ == "__main__":
    # import sys
    # if len(sys.argv) != 2:
    #     print("Usage: figure <image_path>")
    #     sys.exit(1)

    # image_path = sys.argv[1]
    # result = is_blank_page(image_path)
    # print(result)
    app.run(host="0.0.0.0", port=6999)
