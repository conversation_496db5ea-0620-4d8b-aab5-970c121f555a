# -*- coding: utf-8 -*-
"""
@Date       :   2024-02-07
<AUTHOR>   <PERSON><PERSON><PERSON><PERSON>
@Email      :   <EMAIL>
@File       :   test_nsips_comparison_update.py
@Software   :   PyCharm
"""

import allure
from neox_test_common import logger


@allure.epic("薬師丸賢太")
@allure.suite("NSIPS突合")
@allure.sub_suite("最新的情报更新")
@allure.feature("NSIPS突合")
@allure.story("最新的情报更新")
@allure.title("测试用例：最新的情报更新")
def test_nsips_comparison_update(config):
    """
    TestCase: 最新的情报更新
    """
    with allure.step("最新的情报更新"):
        logger.info("< Test :: 最新的情报更新 >")
        assert True
