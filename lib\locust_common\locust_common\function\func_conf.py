#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
@Date       :   2021-12-24
<AUTHOR>   Ku<PERSON>Lu
@Email      :   <EMAIL>
@File       :   func_conf.py
@Software   :   PyCharm
"""

import json
from pathlib import Path
from typing import Any, Dict, List, Mapping, MutableMapping

import orjson
import toml
import yaml


def load_toml(config_file: Path) -> MutableMapping[str, Any] | List[Dict[str, Any]]:
    """
    读取 toml 配置文件
    :param config_file: toml 配置文件
    :return: 字典格式的配置文件内容
    """
    with open(config_file, "r", encoding="utf-8") as f:
        config = toml.load(f)
    return config


def dump_toml(
    config_file: Path, object_dict: Mapping[Any, Any] | List[Mapping[str, Any]]
) -> Path:
    """
    将字典转换成 toml 配置文件
    :param config_file: toml 配置文件
    :param object_dict: 字典对象
    :return: None
    """
    with open(config_file, "w", encoding="utf-8") as f:
        _ = toml.dump(object_dict, f)
    return config_file


def load_yml(config_file: Path) -> Dict[str, Any] | List[Dict[str, Any]]:
    """
    读取 yaml 配置文件
    :param config_file: yaml 配置文件
    :return: 字典格式的配置文件内容
    """
    with open(config_file, "r", encoding="utf-8") as f:
        config = yaml.load(f.read(), Loader=yaml.FullLoader)
    return config


def dump_yml(
    config_file: Path, object_dict: Mapping[Any, Any] | List[Mapping[str, Any]]
) -> Path:
    """
    将字典转换成 yaml 配置文件
    :param config_file: yaml 配置文件
    :param object_dict: 字典对象
    :return: None
    """
    with open(config_file, "w", encoding="utf-8") as f:
        _ = yaml.dump(
            object_dict,
            f,
            default_flow_style=False,
            encoding="utf-8",
            allow_unicode=True,
        )
    return config_file


def load_json_file(json_file: Path) -> Dict[str, Any] | List[Dict[str, Any]]:
    """
    读取 json 文件
    :param json_file: json 文件
    :return: 字典格式的 json 文件内容
    """
    with open(json_file, "r", encoding="utf-8") as f:
        data = json.load(f)
    return data


def dump_json_file(
    json_file: Path, object_dict: Mapping[Any, Any] | List[Mapping[str, Any]]
) -> Path:
    """
    将字典转换成 json 文件
    :param json_file: json 文件路径
    :param object_dict: 字典对象
    :return: None
    """
    with open(json_file, "w") as jf:
        # _ = jf.write(json.dumps(object_dict, indent=2, ensure_ascii=False))
        _ = jf.write(orjson.dumps(object_dict, option=orjson.OPT_INDENT_2).decode())
    return json_file


def conf_to_dict(conf_file: Path) -> Dict[str, str]:
    """
    将 conf 配置文件内容转换成字典
    :param conf_file: conf 文件
    :return: 字典格式的 conf 配置文件内容
    """
    with open(conf_file, "r") as f:
        conf_content = f.read()

    config = {}
    for line in conf_content.splitlines():
        line = line.strip()
        if line and not line.startswith("#"):
            key, value = line.split("=", 1)
            config[key.strip()] = value.strip()
    return config


def dict_to_conf(conf_file: Path, config: Mapping[str, str]) -> Path:
    """
    将字典格式的配置文件内容转换成 conf 配置文件内容后写入 conf 文件
    :param conf_file: conf 文件路径
    :param config: 字典格式的配置文件内容
    :return: conf 配置文件路径
    """
    conf_lines = []
    for key, value in config.items():
        conf_lines.append(f"{key} = {value}")
    conf_content = "\n".join(conf_lines)

    with open(conf_file, "w", encoding="utf-8") as cf:
        cf.write(conf_content)
    return conf_file
