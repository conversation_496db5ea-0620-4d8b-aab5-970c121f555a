# -*- coding: utf-8 -*-
"""
@Date       :   2024-02-01
<AUTHOR>   <PERSON><PERSON><PERSON>u
@Email      :   <EMAIL>
@File       :   powershell.py
@Software   :   PyCharm
"""

import os
from glob import glob
import subprocess as sp


class PowerShell:
    # from scapy
    def __init__(self, coding):
        """
        Initialize a PowerShell object.

        Args:
            coding (str): The encoding type for PowerShell.
        """
        cmd = [self._where('PowerShell.exe'),
               "-NoLogo", "-NonInteractive",  # Do not print headers
               "-Command", "-"]  # Listen commands from stdin
        startupinfo = sp.STARTUPINFO()
        startupinfo.dwFlags |= sp.STARTF_USESHOWWINDOW
        self.popen = sp.Popen(cmd, stdout=sp.PIPE, stdin=sp.PIPE, stderr=sp.STDOUT, startupinfo=startupinfo)
        self.coding = coding

    def __enter__(self):
        return self

    def __exit__(self, a, b, c):
        """
        Clean up the PowerShell object.
        """
        self.popen.kill()

    def run(self, cmd, timeout=60):
        """
        Execute a PowerShell command.

        Args:
            cmd (str): The PowerShell command to execute.
            timeout (int): The timeout for the command in seconds.

        Returns:
            tuple: A tuple containing the command output and any errors.
        """
        b_cmd = cmd.encode(encoding=self.coding)
        try:
            b_outs, errs = self.popen.communicate(b_cmd, timeout=timeout)
        except sp.TimeoutExpired:
            self.popen.kill()
            b_outs, errs = self.popen.communicate()
        outs = b_outs.decode(encoding=self.coding)
        return outs, errs

    @staticmethod
    def _where(filename, dirs=None, env="PATH"):
        """
        Find a file in the system path.

        Args:
            filename (str): The name of the file to find.
            dirs (list, optional): A list of directories to search.
            env (str, optional): The environment variable containing the path.

        Returns:
            str: The full path to the file.
        """
        if dirs is None:
            dirs = []
        if not isinstance(dirs, list):
            dirs = [dirs]
        if glob(filename):
            return filename
        paths = [os.curdir] + os.environ[env].split(os.path.pathsep) + dirs
        try:
            return next(os.path.normpath(match)
                        for path in paths
                        for match in glob(os.path.join(path, filename))
                        if match)
        except (StopIteration, RuntimeError):
            raise IOError("File not found: %s" % filename)

# if __name__ == '__main__':
#     # Example:
#     with PowerShell('UTF-8') as ps:
#         # outs, errs = ps.run(r'Get-ChildItem D:\NeoX -recurse|?{$_.Name -like "*.txt"}|select Name')
#         # outs, errs = ps.run("Get-ChildItem D:\\NeoX -recurse|select Name")
#         outs, errs = ps.run("Get-ChildItem D:\\NeoX|select Name")
#     # print('error:', os.linesep, errs)
#     # print('output:', os.linesep, outs)
#     print(outs)
