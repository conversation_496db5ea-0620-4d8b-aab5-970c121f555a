#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
@Date       :   2024-02-20
<AUTHOR>   <PERSON><PERSON><PERSON><PERSON>
@Email      :   <EMAIL>
@File       :   __init__.py
@Software   :   PyCharm
"""

"""
Kenta Module

This module contains task classes for prescription recognition and processing
workflows in both backend and frontend testing scenarios.

Components:
    be_prescriptions: Backend prescription processing with MongoDB integration
    fe_prescriptions: Frontend prescription processing with HTTP API testing
    general: Common utilities for device management and API interactions
"""
