# -*- coding: utf-8 -*-
"""
@Time     :   2024/10/09 17:56:23
<AUTHOR>   <PERSON><PERSON><PERSON><PERSON>
@Email    :   <EMAIL>
@File     :   global_context.py
@Software :   Cursor
"""

from aenum import extend_enum
from locust_common.param.ExprJmesMap import ExprJmespathMapping as ejm


class ExtendEJM:
    def __init__(self, common_ejm):
        self._common_ejm = common_ejm

    @property
    def extend_ejm(self) -> ejm:
        # global
        extend_enum(self._common_ejm, "ENV_FILE", "global.ENV_FILE")

        # task.self_dev_engine
        extend_enum(
            self._common_ejm,
            "SDE_TASK_WEIGHT",
            "task.self_dev_engine.task_weight",
        )

        # shape.stages
        extend_enum(self._common_ejm, "STAGES", "shape.stages")

        # iteration.interrupt
        extend_enum(self._common_ejm, "INTERRUPT", "iteration.interrupt")

        # iteration.self_dev_engine
        extend_enum(self._common_ejm, "SDE_ITER_TIME_CONF", "iteration.self_dev_engine")

        # param_files.self_dev_engine
        extend_enum(
            self._common_ejm,
            "PARAM_SDE_MODE",
            "param_files.self_dev_engine.mode",
        )
        extend_enum(
            self._common_ejm,
            "PARAM_TAR_ENV",
            "param_files.self_dev_engine.env",
        )
        extend_enum(
            self._common_ejm,
            "PARAM_STRATEGY_SDE",
            "param_files.self_dev_engine.param_data_loop_enable",
        )
        extend_enum(
            self._common_ejm,
            "IMAGES_DIR_PATH",
            "param_files.self_dev_engine.images_dir_path",
        )
        extend_enum(
            self._common_ejm,
            "PARAM_FILE_IMAGES",
            "param_files.self_dev_engine.base_data_filename",
        )

        return self._common_ejm
