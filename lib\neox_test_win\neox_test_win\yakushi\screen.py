#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
@Date       :   2024/5/17
<AUTHOR>   KunoLu
@Email      :   <EMAIL>
@File       :   screen.py
@Software   :   PyCharm
"""

import time
from pathlib import Path
from typing import Tuple

import pywintypes
import win32api
import win32con
import win32gui
import win32print

# Constants
DEFAULT_DPI = 100
DEFAULT_DPI_EXE = "SetDpi.exe"
SHELL_EXECUTE_SUCCESS_THRESHOLD = 32


class Screen:
    """
    Windows screen resolution and DPI management utility.

    This class provides functionality for managing Windows display settings,
    including screen resolution changes and DPI scaling adjustments. It's
    particularly useful for automated testing scenarios where consistent
    display settings are required across different test environments.

    The class supports dynamic configuration through keyword arguments and
    provides both instance methods for configured operations and static
    methods for general screen information retrieval.

    Attributes:
        tar_screen_resolution: Target screen resolution as [width, height] list
        set_dpi_exe: Path to the DPI setting executable utility
        tar_dpi: Target DPI scaling percentage (e.g., 100, 125, 150)

    Example:
        >>> # Initialize with custom settings
        >>> screen = Screen(
        ...     screen_resolution=[1920, 1080],
        ...     set_dpi_exe_path="./tools/SetDpi.exe",
        ...     dpi=125
        ... )
        >>>
        >>> # Set screen resolution
        >>> screen.set_screen_resolution()
        >>>
        >>> # Adjust DPI scaling
        >>> screen.set_dpi()
        >>>
        >>> # Get current screen information
        >>> width, height = Screen.get_real_resolution()
        >>> dpi = Screen.get_dpi()

    Note:
        DPI changes typically require the SetDpi.exe utility and may need
        administrative privileges. Screen resolution changes take effect
        immediately but may cause brief display flickering.
    """

    def __init__(self, **kwargs) -> None:
        """
        Initialize Screen instance with configuration parameters.

        Args:
            **kwargs: Configuration parameters including:
                - screen_resolution: Target resolution as [width, height]
                - set_dpi_exe_path: Path to DPI setting executable
                - dpi: Target DPI scaling percentage

        Example:
            >>> screen = Screen(
            ...     screen_resolution=[1920, 1080],
            ...     dpi=100
            ... )
        """
        # 设置目标屏幕分辨率
        self.tar_screen_resolution = kwargs.get("screen_resolution")

        # 设置 DPI 调整工具路径
        set_dpi_exe_path = kwargs.get("set_dpi_exe_path")
        self.set_dpi_exe = (
            Path(set_dpi_exe_path) if set_dpi_exe_path else Path.cwd() / DEFAULT_DPI_EXE
        )

        # 设置目标 DPI 缩放比例
        self.tar_dpi = kwargs.get("dpi", DEFAULT_DPI)

    def set_screen_resolution(self) -> None:
        """
        Change the screen resolution to the configured target resolution.

        This method modifies the Windows display settings to match the target
        resolution specified during initialization. The change takes effect
        immediately and may cause brief screen flickering.

        Raises:
            AttributeError: If tar_screen_resolution is not set
            ValueError: If the target resolution is invalid
            OSError: If the resolution change fails

        Example:
            >>> screen = Screen(screen_resolution=[1920, 1080])
            >>> screen.set_screen_resolution()

        Note:
            The resolution change is temporary and will revert to the original
            setting when the system is restarted or display settings are
            manually changed.
        """
        if not self.tar_screen_resolution:
            raise AttributeError("Target screen resolution not configured")

        if len(self.tar_screen_resolution) != 2:
            raise ValueError("Screen resolution must be [width, height]")

        # 创建显示模式结构体
        devmode = pywintypes.DEVMODEType()

        # 设置目标分辨率
        devmode.PelsWidth = self.tar_screen_resolution[0]
        devmode.PelsHeight = self.tar_screen_resolution[1]
        devmode.Fields = win32con.DM_PELSWIDTH | win32con.DM_PELSHEIGHT

        # 应用显示设置更改
        result = win32api.ChangeDisplaySettings(devmode, 0)
        if result != win32con.DISP_CHANGE_SUCCESSFUL:
            raise OSError(f"Failed to change screen resolution: {result}")

    @staticmethod
    def get_real_resolution() -> Tuple[int, int]:
        """
        Get the actual physical resolution of the primary display.

        This method retrieves the true hardware resolution of the screen,
        which represents the actual number of pixels available on the display
        device, regardless of any DPI scaling or virtual resolution settings.

        Returns:
            Tuple containing the actual width and height in pixels

        Example:
            >>> width, height = Screen.get_real_resolution()
            >>> print(f"Physical resolution: {width}x{height}")
            Physical resolution: 1920x1080

        Note:
            This resolution may differ from the virtual resolution reported
            by get_screen_size() when DPI scaling is applied.
        """
        # 获取桌面设备上下文句柄
        hDC = win32gui.GetDC(0)

        # 获取屏幕的实际水平和垂直分辨率
        width = win32print.GetDeviceCaps(hDC, win32con.DESKTOPHORZRES)
        height = win32print.GetDeviceCaps(hDC, win32con.DESKTOPVERTRES)

        return width, height

    @staticmethod
    def get_screen_size() -> Tuple[int, int]:
        """
        Get the virtual screen size accounting for DPI scaling.

        This method returns the screen dimensions as reported by the Windows
        system, which may be scaled based on the current DPI settings. This
        is the size that applications typically see and work with.

        Returns:
            Tuple containing the virtual width and height in pixels

        Example:
            >>> width, height = Screen.get_screen_size()
            >>> print(f"Virtual resolution: {width}x{height}")
            Virtual resolution: 1536x864  # (1920x1080 at 125% scaling)

        Note:
            When DPI scaling is applied, this size will be smaller than
            the physical resolution returned by get_real_resolution().
        """
        # 获取系统度量值中的屏幕尺寸
        width = win32api.GetSystemMetrics(0)
        height = win32api.GetSystemMetrics(1)

        return width, height

    @staticmethod
    def get_dpi() -> float:
        """
        Calculates the screen scale rate (DPI) based on the real and screen sizes.

        This function calculates the screen scale rate (DPI) by dividing the real resolution (accounting for any scaling or other factors) by the screen size. The result is then rounded to two decimal places and multiplied by 100 to get the percentage.

        :return: A float representing the screen scale rate (DPI) as a percentage.
        """
        real_resolution = Screen.get_real_resolution()
        screen_size = Screen.get_screen_size()

        screen_scale_rate = round(real_resolution[0] / screen_size[0], 2)
        screen_scale_rate = screen_scale_rate * 100
        return screen_scale_rate

    def set_dpi(self) -> bool:
        """
        Set the screen scale rate (DPI).

        This function runs the executable file specified in set_dpi_exe_path and sets the screen scale rate (DPI) to the value specified in dpi.

        :return: A boolean indicating whether the operation was successful.
        """
        cur_dpi = int(Screen.get_dpi())
        tar_dpi = int(self.tar_dpi)
        print("当前系统缩放率为: ", cur_dpi, "%")
        if cur_dpi == tar_dpi:
            print("当前系统缩放率无需调整！")
            return True
        else:
            try:
                print(f"正在调整为 {tar_dpi}% 缩放率...")
                win32api.ShellExecute(0, "open", self.set_dpi_exe, f" {tar_dpi}", "", 1)
                print("运行中...")
                time.sleep(3)
                print(f"当前系统缩放率已调整为：{tar_dpi}%")
                return True
            except Exception as ex:
                print(ex)
                return False

    def restore_dpi(self, ori_dpi: int) -> bool:
        """
        Restore the screen scale rate (DPI) to its original value.

        This function runs the executable file specified in set_dpi_exe_path and restores the screen scale rate (DPI) to its original value.

        :param ori_dpi: The original screen scale rate (DPI).
        :return: A boolean indicating whether the operation was successful.
        """
        hinst = win32api.ShellExecute(0, "open", self.set_dpi_exe, str(ori_dpi), "", 1)
        print(f"HINST: {hinst}")
        return True if hinst > 32 else False
