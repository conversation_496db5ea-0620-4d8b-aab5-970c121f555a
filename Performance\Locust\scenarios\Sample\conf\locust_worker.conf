########### global param ############
locustfile = locustfiles/<locust_file_name>.py
#####################################

####### no web UI mode param ########
headless = true
worker = true
### 指定 worker 需要连接的 master 的 host (远程分布式使用)
# master-host = x.x.x.x
### 指定 worker 需要连接的 master 的端口 (远程分布式使用)
# master-port = 5557
#####################################

########### special param ###########
### spawn 完成后重置统计信息。
### 在分布式模式下运行时，应在 master 和 worker 上都设置。
reset-stats = true
#####################################
