#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
@Date       :   2025-05-09
<AUTHOR>   <PERSON><PERSON><PERSON><PERSON>
@Email      :   <EMAIL>
@File       :   __init__.py
@Software   :   Cursor
"""

"""
Enquete Module

This module contains task classes for simulating survey/questionnaire processing
in Locust performance testing scenarios. It provides MongoDB-based survey
data handling and processing workflows.

Components:
    enquete: Survey processing task set with MongoDB integration
"""
