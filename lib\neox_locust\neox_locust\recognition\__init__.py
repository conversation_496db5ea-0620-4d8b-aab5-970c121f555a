#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
@Date       :   2025-06-26
<AUTHOR>   <PERSON><PERSON><PERSON><PERSON>
@Email      :   <EMAIL>
@File       :   __init__.py
@Software   :   Cursor
"""

"""
Recognition Module

This module contains task classes for testing GPU-based recognition engines
that perform OCR (Optical Character Recognition) and QR code recognition
on uploaded image files.

Components:
    engine: Recognition engine task set with file upload testing,
            custom assertion logic, and performance metrics collection
"""
