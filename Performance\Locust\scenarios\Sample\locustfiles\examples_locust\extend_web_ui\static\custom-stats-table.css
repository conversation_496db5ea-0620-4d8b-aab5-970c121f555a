/*
 Customises stats tables color, alignment. Adds row highlighting.
 Just a simple extension example. The sky's the limit here :-)
**/

table.stats {
	border-collapse: collapse;
	overflow: hidden;
	box-shadow: 0 0 20px rgba(0,0,0,0.1);
}

table.stats th, td {
	padding: 15px;
	background-color: rgba(255,255,255,0.2);
	color: #fff;
}

table.stats th {
	text-align: left;
}

table.stats thead {
	th {
		background-color: #55608f;
	}
}

table.stats tbody {
	tr {
		&:hover {
			background-color: rgba(255,255,255,0.3);
		}
	}
	td {
		position: relative;
		&:hover {
			&:before {
				content: "";
				position: absolute;
				left: 0;
				right: 0;
				top: -9999px;
				bottom: -9999px;
				background-color: rgba(255,255,255,0.2);
				z-index: -1;
			}
		}
	}
}