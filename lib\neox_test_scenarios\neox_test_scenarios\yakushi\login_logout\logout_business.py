# -*- coding: utf-8 -*-
"""
@Date       :   2024-02-12
<AUTHOR>   <PERSON><PERSON>Lu
@Email      :   <EMAIL>
@File       :   logout_business.py
@Software   :   PyCharm
"""

import allure
from jmespath import search
from neox_test_common import UIA, logger

from neox_test_scenarios import (
    com_get_yakushi_window,
    com_open_homepage_from_float_window,
)


def perform_logout_flow(config: dict) -> None:
    """
    Perform complete logout flow

    Args:
        config (dict): Test configuration data
    """
    # Step 1: Open homepage from float window
    with allure.step("从浮动窗口中打开主页"):
        window_data = {
            "float_window": search("yakushi.modules.common.window.float", config),
            "float_logo_button": search("yakushi.modules.common.button.logo", config),
            "main_window": search("yakushi.modules.common.window.main", config),
            "main_window_button": search("yakushi.modules.common.button.main", config),
        }
        assert com_open_homepage_from_float_window(window_data)

    # Step 2: Click dropdown logout button
    with allure.step("点击主页右上角的下拉按钮后点击下方退出按钮"):
        logger.info("< Step 1 :: 点击主页右上角的下拉按钮 >")
        main_window_auto_id = search(
            "yakushi.modules.common.window.main.auto_id", config
        )
        main_window = com_get_yakushi_window(
            auto_id=main_window_auto_id, searchDepth=1, timeout=1
        )
        user_button_auto_id = search(
            "yakushi.modules.logout.button.user.auto_id", config
        )
        user_button = UIA.ButtonControl(
            parent=main_window, Name="", AutomationId=user_button_auto_id
        )
        logger.info(f"主页右上角的下拉按钮-RECT：[ {user_button.BoundingRectangle} ]")
        _ = UIA.clickButton(button=user_button, waitTime=1)
        # Get center coordinates of the dropdown button
        x, y = UIA.getControlRectCenter(control=user_button)
        logger.info(f"主页右上角的下拉按钮-CENTER-COORD：[ ({x}, {y}) ]")
        # Click logout button below
        offset_y = search("yakushi.modules.logout.button.user.offset_y", config)
        target_y = y + offset_y
        UIA.click(x=x, y=target_y, waitTime=1)
        logger.info(f"下方退出按钮-CLICK-COORD：[ ({x}, {target_y}) ]")

        confirm_logout_window = UIA.WindowControl(
            Name="メッセージ", ClassName="#32770", searchDepth=2
        )
        logger.info(f"确认登出弹窗-RECT：[ {confirm_logout_window.BoundingRectangle} ]")
        assert confirm_logout_window

    # Step 3: Confirm logout in dialog
    with allure.step("在弹出的确认登出弹窗中点击确认按钮"):
        logger.info("< Step 2 :: 在弹出的确认登出弹窗中点击确认按钮 >")
        confirm_logout_window = UIA.WindowControl(
            Name="メッセージ", ClassName="#32770", searchDepth=2
        )
        confirm_button = UIA.ButtonControl(parent=confirm_logout_window, Name="确定")
        logger.info(
            f"确认登出弹窗中的确认按钮-RECT：[ {confirm_button.BoundingRectangle} ]"
        )
        assert UIA.clickButton(button=confirm_button, waitTime=1)

    # Step 4: Verify logout success
    with allure.step("用户登出并跳转至用户登录页面"):
        logger.info("< Step 3 :: 用户登出并跳转至用户登录页面 >")
        login_window = UIA.WindowControl(
            Name="薬師丸賢太", ClassName="Window", searchDepth=1
        )
        logger.info(f"用户登录页面窗口-RECT：[ {login_window.BoundingRectangle} ]")
        assert login_window


def close_application() -> None:
    """
    Close the application completely
    """
    with allure.step("关闭客户端"):
        logger.info("< Step 4 :: 关闭客户端 >")
        login_window = UIA.WindowControl(
            Name="薬師丸賢太", ClassName="Window", searchDepth=1
        )
        _ = UIA.closeWindow(window=login_window)
        logger.info("< Step 4 :: 弹出用户登出确认窗口 >")
        confirm_close_window = UIA.WindowControl(
            Name="メッセージ", ClassName="#32770", searchDepth=2
        )
        logger.info(
            f"用户登出确认弹窗-RECT：[ {confirm_close_window.BoundingRectangle} ]"
        )
        UIA.setWindowTopMost(confirm_close_window)
        logger.info("< Step 4 :: 将用户登出确认弹窗置顶 >")
        confirm_close_button = UIA.ButtonControl(
            parent=confirm_close_window, Name="确定"
        )
        logger.info(
            f"用户登出确认弹窗的确认按钮-RECT：[ {confirm_close_button.BoundingRectangle} ]"
        )
        logger.info("< Step 4 :: 点击用户登出确认弹窗的确认按钮 >")
        assert UIA.clickButton(button=confirm_close_button, waitTime=1)
