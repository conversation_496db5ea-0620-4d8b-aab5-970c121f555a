# -*- coding: utf-8 -*-
"""
@Time     :   2025/06/26 10:56:27
<AUTHOR>   <PERSON><PERSON><PERSON><PERSON>
@Email    :   <EMAIL>
@File     :   engine.py
@Software :   Cursor
"""

from pathlib import Path

from locust import SequentialTaskSet, task
from locust_common.frame.TaskFrame import Fun<PERSON><PERSON>rame
from locust_common.function.func_param import custom_iter_time
from locust_common.param.ExprJmesMap import ExprJ<PERSON>pathMapping as ejm
from locust_common.param.ExprJmesMap import get_conf
from loguru import logger


class EngineTask(SequentialTaskSet):
    """
    Recognition engine HTTP API task set for Locust performance testing.

    This class implements tasks to test the GPU-based recognition engine
    that processes images and returns OCR or QR code recognition results.
    """

    def on_start(self) -> None:
        """
        Initialize task configuration when the user starts.

        Sets up URL endpoints, HTTP headers, file paths, and logging configuration
        for the recognition engine testing session.
        """
        # 配置API端点URL
        self.gpu_api_url = "/gpu_api"

        # 设置HTTP请求方法
        self.method = "POST"

        # 配置HTTP请求头
        self.headers = {
            "Accept": "*/*",
            "Connection": "keep-alive",
        }

        # 获取图像文件目录的绝对路径
        self.images_dir_path = Path(get_conf(ejm.IMAGES_DIR_PATH)).resolve()  # type: ignore

        # 获取识别模式配置
        self.mode = get_conf(ejm.PARAM_SDE_MODE)  # type: ignore

        # 获取环境信息
        self.tar_env = get_conf(ejm.PARAM_TAR_ENV)  # type: ignore

        # 配置日志处理器
        self.locust_log_flag = get_conf(ejm.LOCUST_LOG_FLAG)  # type: ignore

    def _gpu_api_custom_assert(self, response) -> bool:
        """
        Custom assertion logic for GPU API response validation.

        Validates the response based on specific business logic requirements:
        1. HTTP status code should be 200
        2. Result field validation based on content type (QR or OCR)

        Args:
            response: HTTP response object from the GPU API

        Returns:
            bool: True if response meets all validation criteria, False otherwise
        """
        # 检查HTTP状态码是否为200
        check_status_code = response.status_code == 200

        # 初始化断言值
        assert_value = False
        try:
            # 解析响应JSON数据
            response_json = response.json()
            result = response_json.get("result")

            # 条件1：如果result为null，则认为是有效响应
            if result is None:
                assert_value = True
            # 条件2和3：如果result不为null，则检查其字段内容
            elif isinstance(result, dict):
                # 条件2：检查result.qrContent是否为int或string类型
                qr_content = result.get("qrContent")
                if isinstance(qr_content, (int, str)):
                    assert_value = True

                # 条件3：检查result.ocrContent是否为非空字典
                ocr_content = result.get("ocrContent")
                if isinstance(ocr_content, dict) and len(ocr_content) > 0:
                    assert_value = True

        except (ValueError, KeyError) as e:
            # JSON解析失败或键错误时，设置断言值为False
            assert_value = False
            print(f"Failed to parse response JSON: {e}")
            if self.locust_log_flag:
                logger.error(f"Failed to parse response JSON: {e}")

        # 返回状态码检查和业务逻辑断言的组合结果
        return check_status_code and assert_value

    @task
    def gpu_api(self):
        """
        GPU API task for image recognition testing.

        This task uploads image files to the recognition engine and validates
        the response according to business requirements. It supports both
        QR code and OCR recognition modes.
        """
        # 设置任务显示名称
        display_name = "[ 自研引擎 ]: gpu_api"

        # 从队列中获取图像参数数据
        param_data_get_images_task = self.user.param_data_images_queue.get()  # type: ignore
        image_file_name = param_data_get_images_task["image_file_name"]
        image_file_path = self.images_dir_path.joinpath(image_file_name)

        # 根据参数化策略决定是否将数据放回队列（用于循环测试）
        if get_conf(ejm.PARAM_STRATEGY_SDE):  # type: ignore
            self.user.param_data_images_queue.put_nowait(param_data_get_images_task)  # type: ignore

        # 使用通用的文件上传框架进行测试
        try:
            # 创建FuncFrame实例，配置请求参数
            func_frame = FuncFrame(
                method=self.method,
                name=display_name,
                url=self.gpu_api_url,
                headers=self.headers,
                body=None,  # 文件上传不需要body参数
                assert_keys=None,  # 使用自定义断言函数替代简单键值检查
                locust_logger=logger,
                log_flag=self.locust_log_flag,
            )

            # 调用通用文件上传方法，上传图像并验证响应
            _ = func_frame.task_assert_resp_with_file_upload(
                obj=self,
                file_path=image_file_path,
                file_field_name="image_file",  # 图像文件的表单字段名
                content_type="image/jpeg",  # 图像文件的MIME类型
                form_data={
                    "mode": self.mode,
                    "env": self.tar_env,
                },  # 附加的表单数据（识别模式及环境信息）
                custom_assert_func=self._gpu_api_custom_assert,  # 自定义断言函数
            )

        except FileNotFoundError:
            # 处理文件未找到的异常
            print(f"File not found: {image_file_path}")
            if self.locust_log_flag:
                logger.error(f"File not found: {image_file_path}")
        except Exception as e:
            # 处理其他文件上传相关异常
            print(f"Error uploading file: {e}")
            if self.locust_log_flag:
                logger.error(f"Error uploading file: {e}")

    @task
    def stop(self):
        """
        Stop task to control iteration timing and execution flow.

        This task determines whether the current iteration should end based on
        scenario configuration and custom timing logic.
        """
        # 根据配置执行自定义迭代时间控制
        _ = custom_iter_time(get_conf(ejm.SDE_ITER_TIME_CONF))  # type: ignore

        # 根据中断配置决定是否结束当前用户的执行
        if get_conf(ejm.INTERRUPT):  # type: ignore
            self.interrupt()
