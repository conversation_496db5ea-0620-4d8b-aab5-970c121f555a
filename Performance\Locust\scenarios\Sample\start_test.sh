#!/bin/bash
: << !
 @illustration: Locust test 启动脚本
  操作说明:
  bash start_test.sh $1 $2 $3
    $1 运行模式 [ help (帮助文档) / single (单机单进程模式) / master (单机多进程模式 - master) / worker (单机多进程模式 - worker)]
    $2 *.conf 配置文件路径;
    $3 miniconda3 中的 locust 环境名称（不传该参数则选默认环境）
 @author: Kuno
 @time: 2022/08/30
!

##################################请配置以下选项##################################

# 默认在 conda 中安装的 locust python 环境名称
DEFAULT_ENV=locust_v2.23.1

_CONF_FILE=$2

if [ -n "$3" ] ;then
    _ENV_NAME=$3
else
    _ENV_NAME=${DEFAULT_ENV}
fi

# python 解释器的绝对路径
_PY_HOME=/usr/miniconda3/envs/${_ENV_NAME}/bin

# 单机多进程模式下(mp)，每个批次需要传入的 config_filename 的参数化文件的名称 (该文件尾需换行)
_PARAM_CSV=locust_worker_param_filename.csv

# locust 脚本的自定义配置文件 (toml格式)
_CONFIG_TOML=config.toml

##################################以下选项无需配置#################################

_W_DIR=$(command pwd)
_PARAM_CSV_FILEPATH="${_W_DIR}/${_PARAM_CSV}"
_CONFIG_TOML_FILEPATH="${_W_DIR}/${_CONFIG_TOML}"

RED_COLOR='\e[31m'
GREEN_COLOR='\e[32m'
YELLOW_COLOR='\e[33m'
BLUE_COLOR='\e[34m'
SUFFIX_COLOR_TAG='\e[0m'

#####################################启动命令#####################################

if [[ "$1" != "help" && "$1" != "single" && "$1" != "master" && "$1" != "worker" ]]; then
  echo -e "${RED_COLOR} [ERROR] Your \$1 is: ($1). Just support: (help / single / master / worker) ${SUFFIX_COLOR_TAG}"
else
  case $1 in
  help)
    echo -e "${RED_COLOR} [*] Fire locust scripts ${SUFFIX_COLOR_TAG}"
    echo -e "${GREEN_COLOR} [*] Usage: bash start_test.sh \$1 \$2 \$3 ${SUFFIX_COLOR_TAG}"
    echo -e "${BLUE_COLOR} [*] Param: \$1 - exec mode ${SUFFIX_COLOR_TAG}"
    echo -e "${YELLOW_COLOR} [*] Param illustration: ${SUFFIX_COLOR_TAG}"
    echo -e "${YELLOW_COLOR} [*]    help: Docs ${SUFFIX_COLOR_TAG}"
    echo -e "${YELLOW_COLOR} [*]    single: Single Process Mode ${SUFFIX_COLOR_TAG}"
    echo -e "${YELLOW_COLOR} [*]    master: Multiple Process Mode - Master Node ${SUFFIX_COLOR_TAG}"
    echo -e "${YELLOW_COLOR} [*]    worker: Multiple Process Mode - Worker Node ${SUFFIX_COLOR_TAG}"
    echo -e "${BLUE_COLOR} [*] Param: \$2 - your/path/to/*.conf ${SUFFIX_COLOR_TAG}"
    echo -e "${BLUE_COLOR} [*] Param (Optional): \$3 - conda env name (Default: ${DEFAULT_ENV}) ${SUFFIX_COLOR_TAG}"
    ;;
  single)
    single_step=$(command nohup "${_PY_HOME}"/python3 -m locust --config "${_CONF_FILE}" > exec.log 2>&1 &)
    echo -e "${GREEN_COLOR} Single mode command executed: [ nohup ${_PY_HOME}/python3 -m locust --config ${_CONF_FILE} > exec.log 2>&1 & ] ${SUFFIX_COLOR_TAG}"
    ;;
  master)
    master_step=$(command nohup "${_PY_HOME}"/python3 -m locust --config "${_CONF_FILE}" > exec.log 2>&1 &)
    echo -e "${GREEN_COLOR} Master fired: [ nohup ${_PY_HOME}/python3 -m locust --config ${_CONF_FILE} > exec.log 2>&1 & ] ${SUFFIX_COLOR_TAG}"
    ;;
  worker)
    if [ -f "${_PARAM_CSV_FILEPATH}" ]; then
      line_num=0
      IFS=","
      while read -r init_param_filename worker_param_filename_prefix expect_workers; do
        ((line_num++))
        if [[ "$line_num" -gt 1 ]]; then
          for ((i=1; i<=expect_workers; i++))
          do
            worker_idx=$(command printf "%02d" "$i")
            if [[ "${init_param_filename}" != "null" && "${worker_param_filename_prefix}" != "null" ]];then
              worker_param_filename="${worker_param_filename_prefix}_${worker_idx}.txt"
              # 注意：MacOS 下, sed -i 后需要添加 '' , Unix 无需添加
              step1=$(command sed -i "s/${init_param_filename}/${worker_param_filename}/g" "${_CONFIG_TOML_FILEPATH}")
              step2=$(command nohup "${_PY_HOME}"/python3 -m locust --config "${_CONF_FILE}" >> exec.log 2>&1 &)
              echo -e "${GREEN_COLOR} Worker-${worker_idx} fired: Using param file: [${worker_param_filename}] ${SUFFIX_COLOR_TAG}"
              step3=$(command sed -i "s/${worker_param_filename}/${init_param_filename}/g" "${_CONFIG_TOML_FILEPATH}")
            else
              worker_step=$(command nohup "${_PY_HOME}"/python3 -m locust --config "${_CONF_FILE}" >> exec.log 2>&1 &)
              echo -e "${GREEN_COLOR} Worker-${worker_idx} fired: Not using param file! ${SUFFIX_COLOR_TAG}"
            fi
          done
        fi
      done < "${_PARAM_CSV_FILEPATH}"
    else
      echo -e "${RED_COLOR} [ERROR] ${_PARAM_CSV_FILEPATH} is not exists! ${SUFFIX_COLOR_TAG}"
    fi
    ;;
  esac
fi

################################################################################
