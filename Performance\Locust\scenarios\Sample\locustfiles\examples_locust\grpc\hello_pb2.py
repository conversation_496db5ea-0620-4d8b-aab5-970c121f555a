# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: hello.proto
"""Generated protocol buffer code."""
from google.protobuf import descriptor as _descriptor
from google.protobuf import message as _message
from google.protobuf import reflection as _reflection
from google.protobuf import symbol_database as _symbol_database

# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()


DESCRIPTOR = _descriptor.FileDescriptor(
    name="hello.proto",
    package="locust.hello",
    syntax="proto3",
    serialized_options=None,
    create_key=_descriptor._internal_create_key,
    serialized_pb=b'\n\x0bhello.proto\x12\x0clocust.hello"\x1c\n\x0cHelloRequest\x12\x0c\n\x04name\x18\x01 \x01(\t" \n\rHelloResponse\x12\x0f\n\x07message\x18\x01 \x01(\t2U\n\x0cHelloService\x12\x45\n\x08SayHello\x12\x1a.locust.hello.HelloRequest\x1a\x1b.locust.hello.HelloResponse"\x00\x62\x06proto3',
)


_HELLOREQUEST = _descriptor.Descriptor(
    name="HelloRequest",
    full_name="locust.hello.HelloRequest",
    filename=None,
    file=DESCRIPTOR,
    containing_type=None,
    create_key=_descriptor._internal_create_key,
    fields=[
        _descriptor.FieldDescriptor(
            name="name",
            full_name="locust.hello.HelloRequest.name",
            index=0,
            number=1,
            type=9,
            cpp_type=9,
            label=1,
            has_default_value=False,
            default_value=b"".decode("utf-8"),
            message_type=None,
            enum_type=None,
            containing_type=None,
            is_extension=False,
            extension_scope=None,
            serialized_options=None,
            file=DESCRIPTOR,
            create_key=_descriptor._internal_create_key,
        ),
    ],
    extensions=[],
    nested_types=[],
    enum_types=[],
    serialized_options=None,
    is_extendable=False,
    syntax="proto3",
    extension_ranges=[],
    oneofs=[],
    serialized_start=29,
    serialized_end=57,
)


_HELLORESPONSE = _descriptor.Descriptor(
    name="HelloResponse",
    full_name="locust.hello.HelloResponse",
    filename=None,
    file=DESCRIPTOR,
    containing_type=None,
    create_key=_descriptor._internal_create_key,
    fields=[
        _descriptor.FieldDescriptor(
            name="message",
            full_name="locust.hello.HelloResponse.message",
            index=0,
            number=1,
            type=9,
            cpp_type=9,
            label=1,
            has_default_value=False,
            default_value=b"".decode("utf-8"),
            message_type=None,
            enum_type=None,
            containing_type=None,
            is_extension=False,
            extension_scope=None,
            serialized_options=None,
            file=DESCRIPTOR,
            create_key=_descriptor._internal_create_key,
        ),
    ],
    extensions=[],
    nested_types=[],
    enum_types=[],
    serialized_options=None,
    is_extendable=False,
    syntax="proto3",
    extension_ranges=[],
    oneofs=[],
    serialized_start=59,
    serialized_end=91,
)

DESCRIPTOR.message_types_by_name["HelloRequest"] = _HELLOREQUEST
DESCRIPTOR.message_types_by_name["HelloResponse"] = _HELLORESPONSE
_sym_db.RegisterFileDescriptor(DESCRIPTOR)

HelloRequest = _reflection.GeneratedProtocolMessageType(
    "HelloRequest",
    (_message.Message,),
    {
        "DESCRIPTOR": _HELLOREQUEST,
        "__module__": "hello_pb2"
        # @@protoc_insertion_point(class_scope:locust.hello.HelloRequest)
    },
)
_sym_db.RegisterMessage(HelloRequest)

HelloResponse = _reflection.GeneratedProtocolMessageType(
    "HelloResponse",
    (_message.Message,),
    {
        "DESCRIPTOR": _HELLORESPONSE,
        "__module__": "hello_pb2"
        # @@protoc_insertion_point(class_scope:locust.hello.HelloResponse)
    },
)
_sym_db.RegisterMessage(HelloResponse)


_HELLOSERVICE = _descriptor.ServiceDescriptor(
    name="HelloService",
    full_name="locust.hello.HelloService",
    file=DESCRIPTOR,
    index=0,
    serialized_options=None,
    create_key=_descriptor._internal_create_key,
    serialized_start=93,
    serialized_end=178,
    methods=[
        _descriptor.MethodDescriptor(
            name="SayHello",
            full_name="locust.hello.HelloService.SayHello",
            index=0,
            containing_service=None,
            input_type=_HELLOREQUEST,
            output_type=_HELLORESPONSE,
            serialized_options=None,
            create_key=_descriptor._internal_create_key,
        ),
    ],
)
_sym_db.RegisterServiceDescriptor(_HELLOSERVICE)

DESCRIPTOR.services_by_name["HelloService"] = _HELLOSERVICE

# @@protoc_insertion_point(module_scope)
