# -*- coding: utf-8 -*-
"""
@Date       :   2024-02-12
<AUTHOR>   KunoLu
@Email      :   <EMAIL>
@File       :   login_fail.py
@Software   :   PyCharm
"""
import random
import string
import allure
from jmespath import search
from pytest_bdd import given, when, then

from neox_test_common import logger, UIA
from neox_test_scenarios import com_show_desktop, com_open_yakushi_app, com_write_acc_info, com_click_login_btn


@given("显示桌面")
def show_desktop():
    with allure.step("显示桌面"):
        com_show_desktop()


@when("打开Yakushi客户端")
def open_yakushi_app(config):
    with allure.step("打开Yakushi客户端"):
        assert com_open_yakushi_app(config)


@when("在账户信息文本框中输入错误的账户名")
def write_account_name(config):
    with allure.step("在账户信息文本框中输入错误的账户名"):
        user_data = {
            "control": search("yakushi.modules.login.control.box.user", config),
            "log": {
                "debug": "账户信息文本框窗口",
                "info": "< Step 1 :: 在账户信息文本框中输入错误的账户名 >"
            },
            "text": ''.join(random.choice(string.ascii_letters + string.digits) for _ in range(8))
        }

        assert com_write_acc_info(user_data)


@when("在账户密码文本框中输入错误的密码")
def write_account_pwd(config):
    with allure.step("在账户密码文本框中输入错误的密码"):
        pwd_data = {
            "control": search("yakushi.modules.login.control.box.password", config),
            "log": {
                "debug": "账户密码文本框窗口",
                "info": "< Step 2 :: 在账户密码文本框中输入错误的密码 >"
            },
            "text": ''.join(random.choice(string.ascii_letters + string.digits) for _ in range(8))
        }

        assert com_write_acc_info(pwd_data)


@when("点击登录按钮")
def click_login_button(config):
    with allure.step("点击登录按钮"):
        btn_data = {
            "control": search("yakushi.modules.login.control.btn.login", config),
            "log": {
                "debug": "登录按钮",
                "info": "< Step 3 :: 点击登录按钮 >"
            }
        }

        assert com_click_login_btn(btn_data)


@then("登录失败并在跳出的错误信息弹窗中点击确认")
def check_login_status(config):
    with allure.step("登录失败并在跳出的错误信息弹窗中点击确认"):
        logger.info("< Step 4 :: 登录失败并跳出错误信息弹窗 >")
        login_failed_confirm_window = UIA.WindowControl(Name='', ClassName='#32770', searchDepth=2, timeout=3)
        if login_failed_confirm_window is not None:
            logger.debug(f"登录失败错误信息弹窗-RECT：{login_failed_confirm_window.BoundingRectangle}")
            login_failed_confirm_button = UIA.ButtonControl(parent=login_failed_confirm_window, Name='确定')
            logger.debug(f"登录失败错误信息弹窗的确认按钮-RECT：{login_failed_confirm_button.BoundingRectangle}")
            status_click = UIA.clickButton(button=login_failed_confirm_button)
            logger.info("< Step 4 :: 点击登录失败错误信息弹窗的确认按钮 >")
            assert status_click
        else:
            assert False
