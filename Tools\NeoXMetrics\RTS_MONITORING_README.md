# RTS 监控功能说明

## 功能概述

在 NeoXMetrics 类中新增了 Redis TimeSeries (RTS) 监控功能，实现以下三个主要需求：

1. **每分钟数据汇总**：每分钟查询 `rts_src_key` 从当前小时整点到现在的数据之和，写入 `rts_tar_key`
2. **数据转存**：将汇总的数据实时写入目标 Redis TimeSeries
3. **每小时对比数据**：每小时查询上周同日同时间段的 Last 值，写入 `rts_compare_key`

所有时间计算均使用日本时区 (Asia/Tokyo)。

## 技术优化

### 使用 Redis TimeSeries 聚合功能

本实现使用了 Redis TimeSeries 的内置聚合功能，而不是手动计算：

**优势：**
1. **性能更优**：Redis 在服务端进行聚合计算，减少网络传输
2. **内存效率**：不需要传输所有原始数据点到客户端
3. **精确性**：避免浮点数累加可能的精度损失
4. **可扩展性**：适合处理大量数据点的场景

**对比：**
```bash
# 手动计算方式（低效）
TS.RANGE key start end  # 返回所有数据点，客户端求和

# Redis TS 聚合方式（高效）
TS.RANGE key start end AGGREGATION SUM 3600000 ALIGN start  # 服务端聚合
```

## 配置说明

在 `metrics.toml` 配置文件中，RTS 相关配置位于 `[metrics.rts]` 部分：

```toml
[metrics.rts]
source_key = "GRAFANA_NEOX_ENGINE_REQUEST_COUNT"      # 源数据 key
target_key = "GRAFANA_SDE_PRES_COUNT_TODAY"           # 目标数据 key  
compare_key = "GRAFANA_SDE_PRES_COUNT_LAST_WEEKDAY"   # 对比数据 key
```

## 新增方法说明

### 核心业务方法

#### `get_hourly_sum_from_start(ts_key)`
- **功能**：获取当前小时整点到现在的数据之和
- **参数**：`ts_key` - Redis TimeSeries 键名
- **返回**：数据之和（float），无数据时返回 0
- **时区**：日本时间
- **实现**：使用 Redis TS 的 `AGGREGATION SUM` 功能，性能更优
- **命令示例**：`TS.RANGE key start end AGGREGATION SUM 3600000 ALIGN start`

#### `write_to_redis_ts(ts_key, value, timestamp=None)`
- **功能**：写入数据到 Redis TimeSeries
- **参数**：
  - `ts_key` - Redis TimeSeries 键名
  - `value` - 要写入的值
  - `timestamp` - 时间戳（毫秒），默认使用当前日本时间
- **返回**：写入是否成功（bool）

#### `get_last_week_same_hour_last_value(ts_key)`
- **功能**：获取上周同日同时间段的 Last 值
- **参数**：`ts_key` - Redis TimeSeries 键名
- **返回**：Last 值（float），无数据时返回 0
- **时区**：日本时间
- **实现**：使用 Redis TS 的 `AGGREGATION LAST` 功能，性能更优
- **命令示例**：`TS.RANGE key start end AGGREGATION LAST 3600000 ALIGN start`
- **示例**：周一 09:00 执行时，查询上周一 09:00-10:00 时间段的最后一个值

### 定时任务管理方法

#### `start_rts_monitoring()`
- **功能**：启动 RTS 监控任务
- **行为**：
  - 立即执行一次分钟任务
  - 如果当前在整点附近，立即执行小时任务
  - 开始定时调度后续任务

#### `stop_rts_monitoring()`
- **功能**：停止 RTS 监控任务
- **行为**：取消所有定时器，清理资源

## 使用方法

### 方法 1：单独启动 RTS 监控

```python
from pathlib import Path
from neox_metrics import NeoXMetrics

# 创建实例
config_path = Path("metrics.toml")
metrics = NeoXMetrics(config_path)

# 启动 RTS 监控
metrics.start_rts_monitoring()

# 程序会持续运行，执行定时任务
# 使用 Ctrl+C 或调用 metrics.stop_rts_monitoring() 停止
```

### 方法 2：与 Prometheus 监控一起运行

```python
from pathlib import Path
from neox_metrics import main

# 启动完整监控（包括 RTS 和 Prometheus）
config_path = Path("metrics.toml")
main(config_path, enable_rts=True)
```

### 方法 3：使用测试脚本

```bash
# 测试 RTS 功能
python test_rts_monitoring.py

# 测试完整功能
python test_rts_monitoring.py --full
```

## 任务执行时间

- **分钟任务**：每分钟执行一次，在每分钟的开始时刻
- **小时任务**：每小时执行一次，在每小时的整点时刻

## 日志记录

所有 RTS 相关操作都会记录详细日志，包括：
- 数据查询结果
- 写入操作状态
- 任务调度信息
- 错误信息

日志级别为 INFO，格式：`时间戳 - 模块名 - 级别 - 消息`

## 错误处理

- Redis 连接失败：记录错误并继续尝试
- 数据查询异常：返回默认值 0
- 定时任务异常：记录错误并重新调度
- 所有异常都不会中断主程序运行

## 故障排除

### 常见错误及解决方案

#### 1. "TSDB: the key does not exist" 错误

**原因**：Redis 中不存在配置的 TimeSeries key

**系统行为**：
- 代码会检查 key 是否存在，如果不存在会记录错误日志
- 不会自动创建 key，需要手动创建
- 相关操作会返回默认值（0 或 False）并继续运行

**解决方案**：
1. **手动创建所需的 TimeSeries key**：
   ```bash
   # 连接到 Redis 并创建 key
   redis-cli
   TS.CREATE GRAFANA_NEOX_ENGINE_REQUEST_COUNT
   TS.CREATE GRAFANA_SDE_PRES_COUNT_TODAY
   TS.CREATE GRAFANA_SDE_PRES_COUNT_LAST_WEEKDAY
   ```

2. **检查 key 状态**：
   ```bash
   python test_redis_connection.py
   ```
   此脚本会显示每个 key 的状态和创建命令

3. **测试环境快速创建**：
   ```bash
   python test_rts_monitoring.py
   ```
   测试脚本会自动创建测试用的 key（仅用于测试环境）

#### 2. Redis 连接失败

**检查项目**：
1. Redis 服务器是否运行
2. 连接配置（host、port、password）是否正确
3. 网络连接是否正常

#### 3. TimeSeries 模块不可用

**错误信息**：`unknown command 'TS.CREATE'`

**解决方案**：
- 确保 Redis 服务器加载了 TimeSeries 模块
- 安装 RedisTimeSeries：`redis-server --loadmodule /path/to/redistimeseries.so`

### 测试工具

#### 1. Redis 连接测试
```bash
python test_redis_connection.py
```
**功能**：
- 测试 Redis 基本连接
- 检查 TimeSeries 模块是否可用
- 显示配置的 RTS keys 状态
- 提供创建缺失 key 的命令

**输出示例**：
```
✅ Redis 连接和 TimeSeries 模块测试成功！
   source_key (GRAFANA_NEOX_ENGINE_REQUEST_COUNT): ✅ 已存在
   target_key (GRAFANA_SDE_PRES_COUNT_TODAY): ❌ 不存在
      请手动创建: TS.CREATE GRAFANA_SDE_PRES_COUNT_TODAY
```

#### 2. RTS 功能测试
```bash
# 测试 RTS 功能（会自动创建测试数据）
python test_rts_monitoring.py

# 测试完整功能（RTS + Prometheus）
python test_rts_monitoring.py --full
```
**功能**：
- 自动创建测试用的 TimeSeries keys
- 生成测试数据（当前小时和上周数据）
- 测试所有 RTS 核心方法
- 可选择运行完整监控系统

## 注意事项

1. 确保 Redis 服务器支持 TimeSeries 模块
2. **必须手动创建所需的 TimeSeries keys**，代码不会自动创建
3. 日本时区计算依赖 pendulum 库
4. 定时任务使用 daemon 线程，主程序退出时会自动清理
5. 建议在生产环境中配置适当的日志轮转

## 部署前准备

在启动 RTS 监控之前，请确保在 Redis 中创建所需的 TimeSeries keys：

```bash
# 连接到 Redis
redis-cli

# 创建配置文件中定义的 TimeSeries keys
TS.CREATE GRAFANA_NEOX_ENGINE_REQUEST_COUNT
TS.CREATE GRAFANA_SDE_PRES_COUNT_TODAY
TS.CREATE GRAFANA_SDE_PRES_COUNT_LAST_WEEKDAY
```

或者使用测试脚本检查 key 状态：
```bash
python test_redis_connection.py
```

## 部署检查清单

在生产环境部署 RTS 监控功能前，请确认以下项目：

### ✅ 环境检查
- [ ] Redis 服务器正常运行
- [ ] Redis 已加载 TimeSeries 模块
- [ ] 网络连接正常
- [ ] Python 依赖已安装（redis, pendulum）

### ✅ 配置检查
- [ ] `metrics.toml` 配置文件正确
- [ ] Redis 连接参数正确（host, port, password）
- [ ] RTS keys 配置正确

### ✅ TimeSeries Keys 检查
- [ ] `GRAFANA_NEOX_ENGINE_REQUEST_COUNT` 已创建
- [ ] `GRAFANA_SDE_PRES_COUNT_TODAY` 已创建
- [ ] `GRAFANA_SDE_PRES_COUNT_LAST_WEEKDAY` 已创建

### ✅ 功能测试
- [ ] 运行 `python test_redis_connection.py` 成功
- [ ] 运行 `python test_rts_monitoring.py` 成功
- [ ] 日志输出正常，无错误信息

### ✅ 监控启动
```bash
# 启动完整监控
python neox_metrics.py

# 或使用后台运行
nohup python neox_metrics.py > neox_metrics.log 2>&1 &
```

完成以上检查后，RTS 监控功能即可正常运行。
