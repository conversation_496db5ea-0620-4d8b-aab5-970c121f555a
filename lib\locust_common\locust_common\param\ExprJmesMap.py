#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
@Date       :   2022-08-15
<AUTHOR>   <PERSON><PERSON><PERSON><PERSON>
@Email      :   <EMAIL>
@File       :   ExprJmesMap.py
@Software   :   PyCharm
"""

from enum import Enum
from functools import partial
from pathlib import Path
from typing import Any, List, Optional, Type

from jmespath import search
from loguru._logger import Logger

from locust_common.function.func_conf import load_toml
from locust_common.function.func_log import set_log

# Constants
DEFAULT_CONFIG_FILE = "config.toml"
LOG_EXPR = "log.locust_log_flag"


class ExprJmespathMapping(Enum):
    """
    JMESPath expression mapping enumeration for configuration access.

    This enumeration provides a structured way to access configuration values
    using JMESPath expressions and includes utility methods for file path
    manipulation and logging setup.
    """

    # Configuration data access
    CONF_FILE = DEFAULT_CONFIG_FILE
    CONF_DATA = partial(load_toml)

    # Logging configuration
    LOCUST_LOG_FLAG = LOG_EXPR

    def __call__(self, *args, **kwargs) -> Any:
        """
        Make enum callable to execute associated functions.

        Args:
            *args: Variable length argument list
            **kwargs: Arbitrary keyword arguments

        Returns:
            Result of the associated function call
        """
        return self.value(*args, **kwargs)  # type: ignore

    def join_path(self) -> Path:
        """
        Join configuration value with parameter directory path.

        Creates a path by joining the current working directory's 'paramlist'
        subdirectory with the configuration value.

        Returns:
            Path object representing the joined path

        Example:
            >>> enum_item = ExprJmespathMapping.CONF_FILE
            >>> path = enum_item.join_path()
            >>> print(path)  # /current/dir/paramlist/config.toml
        """
        param_dir_path = Path.cwd().joinpath("paramlist")
        return param_dir_path.joinpath(self.get_config_value(self))

    def join_path_list(self) -> List[Path]:
        """
        Join multiple configuration values with parameter directory path.

        Creates a list of paths by joining the current working directory's
        'paramlist' subdirectory with each file in the configuration list.

        Returns:
            List of Path objects representing the joined paths

        Example:
            >>> enum_item = ExprJmespathMapping.CONF_FILE
            >>> paths = enum_item.join_path_list()
            >>> print(paths)  # [/current/dir/paramlist/file1.txt, ...]
        """
        param_dir_path = Path.cwd().joinpath("paramlist")
        config_files = self.get_config_value(self)
        return [param_dir_path.joinpath(file) for file in config_files]

    def set_internal_log(self) -> Optional[Type[Logger]]:
        """
        Set up internal logging based on configuration.

        Configures internal logging using the configuration value associated
        with this enum item as the enable flag.

        Returns:
            Configured Logger instance if logging is enabled, None otherwise

        Example:
            >>> log_flag_enum = ExprJmespathMapping.LOCUST_LOG_FLAG
            >>> logger = log_flag_enum.set_internal_log()
            >>> if logger:
            ...     logger.debug("Logging is configured")
        """
        log_enabled = self.get_config_value(self)
        return set_log(log_enabled, Logger, "DEBUG")

    def get_enum_value(self) -> Any:
        """
        Get the raw value of this enumeration item.

        Returns:
            The underlying value of the enum item

        Example:
            >>> enum_item = ExprJmespathMapping.CONF_FILE
            >>> value = enum_item.get_enum_value()
            >>> print(value)  # "config.toml"
        """
        return self.value

    @staticmethod
    def get_config_value(expr: "ExprJmespathMapping") -> Any:
        """
        Retrieve configuration value using JMESPath expression.

        Loads the configuration file and applies the JMESPath expression
        to extract the desired configuration value.

        Args:
            expr: ExprJmespathMapping enum item containing the expression

        Returns:
            Configuration value extracted by the JMESPath expression

        Example:
            >>> flag_expr = ExprJmespathMapping.LOCUST_LOG_FLAG
            >>> log_enabled = ExprJmespathMapping.get_config_value(flag_expr)
            >>> print(log_enabled)  # True or False

        Raises:
            FileNotFoundError: If the configuration file does not exist
            ValueError: If the JMESPath expression is invalid
        """
        config_file_path = Path(ExprJmespathMapping.CONF_FILE.value)
        config_data = ExprJmespathMapping.CONF_DATA(config_file_path)
        return search(expr.get_enum_value(), config_data)


def get_conf(enum_expr: ExprJmespathMapping) -> Any:
    """
    Convenience function to get configuration value from enum expression.

    This function provides a simple interface to retrieve configuration
    values using ExprJmespathMapping enum items.

    Args:
        enum_expr: ExprJmespathMapping enum item

    Returns:
        Configuration value associated with the enum expression

    Example:
        >>> log_flag = get_conf(ExprJmespathMapping.LOCUST_LOG_FLAG)
        >>> print(f"Logging enabled: {log_flag}")
    """
    return ExprJmespathMapping.get_config_value(enum_expr)
