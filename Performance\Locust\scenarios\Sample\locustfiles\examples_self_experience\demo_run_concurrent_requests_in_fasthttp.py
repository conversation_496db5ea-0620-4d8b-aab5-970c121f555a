# -*- coding: utf-8 -*-
"""
@Date       :   2023-02-01
<AUTHOR>   <PERSON><PERSON><PERSON><PERSON>
@Email      :   <EMAIL>
@File       :   demo_run_concurrent_requests_in_fasthttp.py
@Software   :   PyCharm
"""
from locust import task
from gevent.pool import Pool


# A single FastHttpUser/geventhttpclient session can run concurrent requests,
# you just have to launch greenlets for each request:

@task
def t(self):
    def concurrent_request(url):
        self.client.get(url)

    pool = Pool()
    urls = ["/url1", "/url2", "/url3"]
    for tar_url in urls:
        pool.spawn(concurrent_request, tar_url)
    pool.join()
