# -*- coding: utf-8 -*-
"""
@Time     :   2024/10/09 17:56:23
<AUTHOR>   <PERSON><PERSON><PERSON><PERSON> 
@Email    :   <EMAIL>
@File     :   global_context.py
@Software :   Cursor
"""

from aenum import extend_enum
from locust_common.param.ExprJmesMap import ExprJmespathMapping as ejm


class ExtendEJM:

    def __init__(self, common_ejm):
        self._common_ejm = common_ejm

    @property
    def extend_ejm(self) -> ejm:
        # global
        extend_enum(self._common_ejm, "ENV_FILE", "global.ENV_FILE")
        extend_enum(self._common_ejm, "ENV_HOST", "global.ENV_HOST")
        extend_enum(self._common_ejm, "API_VERSION", "global.API_VERSION")

        # task.prescriptions
        extend_enum(self._common_ejm, "PRES_TASK_WEIGHT",
                    "task.prescriptions.task_weight")

        # shape.stages
        extend_enum(self._common_ejm, "STAGES", "shape.stages")

        # iteration.prescriptions
        extend_enum(self._common_ejm, "PRES_ITER_TIME_CONF",
                    "iteration.prescriptions")

        # param_files.prescriptions
        extend_enum(self._common_ejm, "PARAM_STRATEGY_PRES",
                    "param_files.prescriptions.param_data_loop_enable")
        extend_enum(self._common_ejm, "PARAM_FILE_PRES",
                    "param_files.prescriptions.base_data_filename")

        # param.prescriptions.upload_dirname
        extend_enum(self._common_ejm, "PARAM_PRES_UPLOAD_DIRNAME",
                    "param.prescriptions.upload_dirname")
        # param.prescriptions.pres_identify_type
        extend_enum(self._common_ejm, "PARAM_PRES_IDENTIFY_TYPE",
                    "param.prescriptions.pres_identify_type")
        # param.prescriptions.pres_file_type
        extend_enum(self._common_ejm, "PARAM_PRES_FILE_TYPE",
                    "param.prescriptions.pres_file_type")

        # log
        extend_enum(self._common_ejm, "INTERNAL_LOG_FLAG",
                    "log.internal_log_flag")

        return self._common_ejm
