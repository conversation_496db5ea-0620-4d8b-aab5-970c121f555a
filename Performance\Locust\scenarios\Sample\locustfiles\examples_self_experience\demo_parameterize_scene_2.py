#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
@Date       :   2021-10-31
<AUTHOR>   KunoLu
@Email      :   <EMAIL>
@File       :   demo_parameterize_scene_2
@Software   :   PyCharm
"""
import os
import json
import time
import queue
from pathlib import Path
from locust import HttpUser, SequentialTaskSet, task, constant


class UserTask(SequentialTaskSet):

    @task(1)
    def export_audit_trail_excel(self):
        """测试数据唯一性，不循环取数，保证虚拟用户使用的数据不重复(唯一)，并且数据取完，测试结束"""
        # get_nowait() 取不到数据直接崩溃，直接跳到except； get() 取不到数据会一直等待
        try:
            # 从队列中取出数据
            param_dict = eval(self.user.user_data_queue.get_nowait())
            # API地址
            url = "/api/audit-trail/audit-log/export"
            # 请求头
            headers = {
                "Connection": "keep-alive",
                "Content-Type": "application/json",
                "Cookie": f"usr={param_dict['usr_cookie']}"
            }
            # 请求体
            body = {
                "criteria": {
                    "project_id": f"{param_dict['study_id']}",
                    "collect_criteria": {
                        "project_env_id": f"{param_dict['env_id']}"
                    },
                    "source_services": [
                        "CORE",
                        "COLLECT",
                        "CONSTRUCT"
                    ]
                },
                "file_type": "EXCEL",
                "language": "zh-CN",
                "request_url": "https://perf.clicktrial.cn/",
                "export_timestamp": int(round(time.time() * 1000)),
                "role_permission_params": "{\"role_id\":\"{0}'\",\"resource_paths\":[{\"resource_path\":[{\"type\":\"Project\",\"field\": \"id\",\"value\":\"{1}\"},{\"type\":\"ProjectEnv\",\"field\":\"id\",\"value\":\"{2}\"}]}],\"permission\":\"FILE_DOWNLOAD\"}".format(
                    param_dict['role_id'], param_dict['study_id'], param_dict['env_id'])
            }
            with self.client.post(name="export_audit_trail_excel", url=url, headers=headers, data=json.dumps(body),
                                  catch_response=True) as response:
                # assert '.zip' in response  # 断言，判断接口返回是否成功
                response.success() if '.zip' in response.content.decode() else response.failure(
                    f"FailTrans is: [export_audit_trail_excel]\nFail msg is:\n{response}")
        # 使用get_nowait()方法，取不到数据，就进入except
        except queue.Empty:
            print("测试数据已用完，本次测试结束！")
            exit(0)


class User(HttpUser):
    tasks = [UserTask]
    wait_time = constant(0)

    param_info = ["paramlist", "AT_040W_user_data.txt"]
    param_path = Path.cwd().parent.joinpath(param_info[0])
    param_file = param_path.joinpath(param_info[1])

    with param_file.open('r', encoding='utf-8') as f:
        param_list = f.readlines()
    key_list = param_list[0].strip().split(',')
    param_dict_in_list = []
    for i, v in enumerate(param_list):
        if i > 0:
            value_list = v.strip().split(',')
            if len(key_list) == len(value_list):
                param_dict_in_list.append(dict(zip(key_list, value_list)))
    user_data_queue = queue.Queue()
    if param_dict_in_list:
        for data in param_dict_in_list:
            user_data_queue.put_nowait(data)
            # print(user_data_queue.get())


if __name__ == '__main__':
    path_info = ["conf", "locust.conf"]
    conf_abs_file = Path.cwd().parent.joinpath(*path_info)
    os.system(f'locust --config {conf_abs_file}')
