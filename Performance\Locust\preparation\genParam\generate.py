# -*- coding: utf-8 -*-
"""
@Time     :   2025/01/21 14:31:08
<AUTHOR>   KunoLu 
@Email    :   <EMAIL>
@File     :   generate.py
@Software :   VSCode
"""

import pandas as pd
from pathlib import Path
from pdf2image import convert_from_path
import uuid
import toml
import shutil
import logging
import os


# 配置日志
def setup_logging() -> logging.Logger:
    log_file = Path(__file__).parent / "run.log"

    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(levelname)s - %(message)s',
        handlers=[
            logging.FileHandler(log_file, mode='w'),  # 覆盖写入日志文件
        ])
    return logging.getLogger(__name__)


logger = setup_logging()


def clear_dir_contents(target_path: Path) -> None:
    """
    清空指定目录下的所有文件和子目录
    
    参数:
    target_path (Path): 要清空的目录路径
    """
    for item in target_path.iterdir():
        if item.is_file() or item.is_symlink():
            item.unlink()  # 删除文件或符号链接
            logger.info(f"已删除文件: {item}")
        elif item.is_dir():
            shutil.rmtree(item)  # 递归删除子目录
            logger.info(f"已删除目录: {item}")
        else:
            logger.info(f"文件类型未知: {item}")


def convert_pdf_to_jpeg(input_dir: str | Path, output_dir: str | Path) -> None:
    """
    将指定目录下的 PDF 文件转换为 JPEG 图片
    
    参数:
    input_dir (str/Path): 包含 PDF 文件的输入目录路径
    output_dir (str/Path): 保存 JPEG 图片的输出目录路径
    
    功能:
    - 递归查找 input_dir 下的所有 PDF 文件
    - 将每个 PDF 文件的每一页转换为 JPEG 图片
    - 在 output_dir 下保持与 input_dir 相同的目录结构
    - 如果目标文件已存在，先删除该文件
    - 图片命名格式: {pdf文件名}_{总页数}_{当前页码}.jpeg
    """
    input_path = Path(input_dir)
    output_path = Path(output_dir)

    if output_path.exists() and output_path.is_dir():
        clear_dir_contents(output_path)

    for pdf_path in input_path.rglob("*.pdf"):
        try:
            relative_path = pdf_path.relative_to(input_path).parent
            output_subdir = output_path / relative_path
            output_subdir.mkdir(parents=True, exist_ok=True)

            # 转换 PDF 为图片
            images = convert_from_path(pdf_path)
            logger.info(f"成功转换 {pdf_path}，共 {len(images)} 页")

            for i, image in enumerate(images):
                image_name = f"{pdf_path.stem}-{uuid.uuid4()}_{len(images)}_{i+1}.jpeg"
                image_path = output_subdir / image_name

                # 如果目标文件已存在，先删除
                if image_path.exists():
                    os.remove(image_path)

                image.save(image_path, "JPEG")
                logger.debug(f"保存图片: {image_path}")

        except Exception as e:
            logger.error(f"转换 {pdf_path} 失败: {str(e)}")
            continue


def copy_jpeg_files(jpeg_dir: str | Path, merged_dir: str | Path) -> None:
    """
    将分散的 JPEG 文件复制到 merged 目录
    
    参数:
    jpeg_dir (str/Path): 包含 JPEG 图片的源目录路径
    merged_dir (str/Path): 合并后的目标目录路径
    
    功能:
    - 递归查找 jpeg_dir 下的所有 JPEG 文件
    - 将所有 JPEG 文件复制到 merged_dir
    - 如果存在同名文件，则在文件名后添加数字后缀
    - 保留原始文件
    """
    merged_path = Path(merged_dir)
    merged_path.mkdir(parents=True, exist_ok=True)

    for jpeg_path in Path(jpeg_dir).rglob("*.jpeg"):
        try:
            dest_path = merged_path / jpeg_path.name
            counter = 1
            while dest_path.exists():
                dest_path = merged_path / f"{jpeg_path.stem}_{counter}{jpeg_path.suffix}"
                counter += 1
            shutil.copy2(str(jpeg_path), str(dest_path))
            logger.debug(f"复制文件: {jpeg_path} -> {dest_path}")
        except Exception as e:
            logger.error(f"复制 {jpeg_path} 失败: {str(e)}")
            continue


def generate_pres_csv(jpeg_dir: str | Path, csv_dir: str | Path,
                      config: dict) -> None:
    """
    根据 JPEG 图片生成 CSV 文件
    
    参数:
    jpeg_dir (str/Path): 包含 JPEG 图片的目录路径
    csv_dir (str/Path): 保存 CSV 文件的输出目录路径
    config (dict): 配置文件内容
    
    功能:
    - 递归查找 jpeg_dir 下的所有 JPEG 文件
    - 根据 config 中的配置生成 CSV 文件
    - CSV 文件包含 config 中定义的字段和图片文件名
    - 每个 JPEG 文件对应 CSV 文件中的一行
    - 最后一行不包含换行符
    """
    csv_filename = config.get("csv", {}).get("output_filename", "output")
    csv_path = Path(csv_dir) / f"{csv_filename}.csv"

    fields = config.get("csv", {}).get("fields", {})
    field_names = list(fields.keys())
    field_names.append("image_name")

    # 获取所有JPEG文件并按pdf文件名和当前pdf页数排序
    jpeg_files = sorted(
        Path(jpeg_dir).rglob("*.jpeg"),
        key=lambda x: (
            x.name.split("-")[0],  # pdf文件名
            int(x.name.split("_")[-1].split(".")[0])  # 当前pdf页数
        ))
    total_files = len(jpeg_files)

    with csv_path.open("w") as csv_file:
        # 写入表头
        csv_file.write(",".join(field_names))
        csv_file.write("\n")

        # 写入数据行
        for i, jpeg_file in enumerate(jpeg_files):
            row = [str(fields.get(key, "")) for key in fields]
            row.append(jpeg_file.name)
            if i < len(jpeg_files) - 1:
                csv_file.write(",".join(row) + "\n")
            else:
                csv_file.write(",".join(row))

    logger.info(f"成功生成 CSV 文件: {csv_path}，共 {total_files} 条记录")

    # 生成 CSV 后，根据 config.csv.aggreate 的值决定是否生成 param_data_pres-aggregated.csv
    if config.get("csv", {}).get("aggregate", False):
        logger.info("生成聚合处方图片的 CSV 文件...")
        if csv_path.exists():
            post_csv_path = Path(csv_dir) / f"{csv_filename}-aggregated.csv"
            try:
                # 读取原始CSV
                df = pd.read_csv(csv_path)

                # 定义分组条件：提取image_name第一个"-"前的字符串，并与其他列组合
                def get_prefix(s):
                    return s.split("-")[0]

                # 将 config.csv.fields 中的字段和 image_name 字段组合作为分组条件
                field_names = list(config['csv']['fields'].keys())
                group_keys = field_names + [df['image_name'].apply(get_prefix)]
                grouped = df.groupby(group_keys)

                # 聚合处理
                aggregated_rows = []
                for _, group in grouped:
                    if len(group) > 1:
                        # 合并image_name
                        merged_row = group.iloc[0].copy()
                        merged_row['image_name'] = "|".join(
                            group['image_name'].tolist())
                        aggregated_rows.append(merged_row)
                    else:
                        # 单行直接保留
                        aggregated_rows.append(group.iloc[0])

                # 创建新DataFrame
                new_df = pd.DataFrame(aggregated_rows)

                # 保存聚合文件
                new_df.to_csv(post_csv_path, index=False)
                logger.info(f"成功生成聚合文件: {post_csv_path}")
                if post_csv_path.exists():
                    # 读取文件内容并删除最后一行的换行符
                    with open(post_csv_path, 'r') as file:
                        lines = file.readlines()
                        # 删除最后一行的换行符
                        if lines:
                            lines[-1] = lines[-1].rstrip('\n')
                    # 将处理后的内容写入最终文件
                    with open(post_csv_path, 'w') as file:
                        file.writelines(lines)
                    logger.info(f"成功格式化聚合文件: {post_csv_path}")

            except Exception as e:
                logger.error(f"生成聚合文件失败: {str(e)}")
                raise
        else:
            logger.error(f"目标 CSV 文件不存在: {csv_path}")


def generate_enquete_csv(jpeg_dir: str | Path, csv_dir: str | Path,
                         config: dict) -> None:
    """
    根据问卷调查图片生成 CSV 文件
    
    参数:
    jpeg_dir (str/Path): 包含 JPEG/JPG 图片的目录路径
    csv_dir (str/Path): 保存 CSV 文件的输出目录路径
    config (dict): 配置文件内容

    功能:
    - 递归查找 jpeg_dir 下的所有 JPEG/JPG 文件
    - 根据 config 中的配置生成 CSV 文件
    - CSV 文件包含 config 中定义的字段和图片文件名
    - 每个图片文件对应 CSV 文件中的一行
    - 最后一行不包含换行符
    """
    # 获取CSV文件名
    csv_filename = config.get("csv", {}).get("output_filename", "output")
    csv_path = Path(csv_dir) / f"{csv_filename}.csv"

    # 确保输出目录存在
    Path(csv_dir).mkdir(parents=True, exist_ok=True)

    # 获取字段名列表
    fields = config.get("csv", {}).get("fields", {})
    field_names = list(fields.keys())
    field_names.append("image_name")

    # 获取所有JPEG和JPG文件
    jpeg_files = []
    jpeg_files.extend(list(Path(jpeg_dir).rglob("*.jpeg")))
    jpeg_files.extend(list(Path(jpeg_dir).rglob("*.jpg")))
    jpeg_files = sorted(jpeg_files)

    total_files = len(jpeg_files)

    # 生成CSV文件
    with csv_path.open("w") as csv_file:
        # 写入表头
        csv_file.write(",".join(field_names))
        csv_file.write("\n")

        # 写入数据行
        for i, jpeg_file in enumerate(jpeg_files):
            row = [str(fields.get(key, "")) for key in fields]
            row.append(jpeg_file.name)
            if i < total_files - 1:
                csv_file.write(",".join(row) + "\n")
            else:
                csv_file.write(",".join(row))

    logger.info(f"成功生成问卷调查 CSV 文件: {csv_path}，共 {total_files} 条记录")


def main_pres(config: dict) -> None:
    """
    主函数，执行处方 PDF 转换和 CSV 生成流程
    
    功能:
    - 设置输入输出目录路径
    - 读取配置文件
    - 调用 convert_pdf_to_jpeg 转换 PDF 文件
    - 根据 merge 配置决定是否复制 JPEG 文件到 merged 目录
    - 调用 generate_csv 生成 CSV 文件
    """
    input_dir = BASE_DIR / "input" / "pdf"
    jpeg_dir = BASE_DIR / "output" / "jpeg"
    csv_dir = BASE_DIR / "output" / "csv"
    merged_dir = jpeg_dir / "merged"

    logger.info("开始 PDF 转换...")
    convert_pdf_to_jpeg(input_dir, jpeg_dir)

    if config.get("csv", {}).get("merge", False):
        logger.info("开始合并 JPEG 文件...")
        copy_jpeg_files(jpeg_dir, merged_dir)
        logger.info("开始生成 CSV 文件...")
        generate_pres_csv(merged_dir, csv_dir, config)
    else:
        logger.info("开始生成 CSV 文件...")
        generate_pres_csv(jpeg_dir, csv_dir, config)

    logger.info("处理完成")


def main_enquete(config: dict) -> None:
    """
    主函数，执行问卷调查图片生成 CSV 生成流程
    
    功能:
    - 设置输入输出目录路径
    - 读取配置文件
    - 调用 generate_csv 生成 CSV 文件
    """
    input_dir = BASE_DIR / "input" / "enquete"
    csv_dir = BASE_DIR / "output" / "csv"

    # 确保输出目录存在
    csv_dir.mkdir(parents=True, exist_ok=True)

    logger.info("开始生成问卷调查 CSV 文件...")
    generate_enquete_csv(input_dir, csv_dir, config)

    logger.info("问卷调查 CSV 生成完成")


if __name__ == "__main__":
    BASE_DIR = Path(__file__).parent

    config_path = BASE_DIR / "config.toml"
    with config_path.open() as f:
        CONFIG = toml.load(f)

    category = CONFIG.get("csv", {}).get("category")
    if category in ("p", "pres", "prescription"):
        main_pres(CONFIG)
    elif category in ("e", "enquete"):
        main_enquete(CONFIG)
    else:
        raise ValueError(f"无效的类别: {category}")
