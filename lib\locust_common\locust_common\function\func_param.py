#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
@Date       :   2021-12-26
<AUTHOR>   <PERSON><PERSON>Lu
@Email      :   <EMAIL>
@File       :   func_param.py
@Software   :   PyCharm
"""

import csv
import random
import re
import time
from pathlib import Path
from typing import Any, Dict, Generator, List, Mapping, Optional, Tuple, Type, Union
from uuid import uuid4

from jmespath import search
from loguru._logger import Logger

# Type aliases for better readability
ParamDataSet = Mapping[str, List[Mapping[str, str]]]
ReplaceDataSet = List[Mapping[str, str]]
StageData = Dict[str, Union[float, int, List[str]]]
IterTimeConf = Dict[str, Union[str, int, float, List[Union[int, float]]]]


def p_uuid() -> str:
    """
    Generate a UUID string.

    Returns:
        UUID string representation

    Example:
        >>> uuid = p_uuid()
        >>> len(uuid)
        36
    """
    return str(uuid4())


def map_param_data(
    param_body: Mapping[Any, Any],
    param_dict: Mapping[str, Any],
    scopes: Optional[Mapping[str, Any]] = None,
) -> Dict[Any, Any] | None:
    """
    Map parameter data with replacement values and method evaluations.

    This function processes parameter body data by replacing paramlist values
    with actual parameter values and evaluating method expressions.

    Args:
        param_body: Parameter body containing paramlist and method definitions
        param_dict: Dictionary containing parameter replacement values
        scopes: Optional scope for eval() method execution

    Returns:
        Updated parameter body with mapped values, or None if param_body is empty

    Example:
        >>> param_body = {"paramlist": {"user": "username"}, "method": {"timestamp": "int(time.time())"}}
        >>> param_dict = {"username": "test_user"}
        >>> scopes = {"time": time}
        >>> result = map_param_data(param_body, param_dict, scopes)
    """
    if param_body:
        param_list = search("paramlist", param_body)
        if param_list:
            for key, value in param_list.items():
                param_list.update({key: param_dict[value]})
        param_method = search("method", param_body)
        if param_method:
            for key, value in param_method.items():
                param_method.update({key: eval(value, scopes)})  # type: ignore
        return param_body  # type: ignore


def fmt_str_with_expr(
    replace_data_set: List[Mapping[str, str]], init_data: str
) -> str | None:
    """
    Format string with expression-based replacement.

    Uses regular expressions to replace parts of the initial string
    based on the provided replacement dataset.

    Args:
        replace_data_set: List of replacement mappings containing key, expr, and new values
        init_data: Initial string data to be formatted

    Returns:
        Formatted string with replacements applied, or None if no replacements

    Example:
        >>> replace_set = [{"key": "user", "expr": r"{{user}}", "new": "john"}]
        >>> init_str = "Hello {{user}}!"
        >>> result = fmt_str_with_expr(replace_set, init_str)
        >>> print(result)  # "Hello john!"
    """

    def _each_internal_deal(key: str, expr: str, new: str, old: str) -> str:
        """
        Internal function to handle individual replacement operations.

        Args:
            key: Key to check for existence in the old string
            expr: Regular expression pattern for replacement
            new: New value to replace with
            old: Original string to perform replacement on

        Returns:
            Updated string with replacement applied
        """
        if key in old:
            return re.sub(r"{}".format(expr), new, old)
        else:
            return old

    replaced_str = None

    for index, each in enumerate(replace_data_set):
        replaced_str = _each_internal_deal(
            each["key"], each["expr"], each["new"], init_data
        )

    return replaced_str


def gen_requests_info(
    req_data_set: Mapping[str, List[Mapping[str, str]]],
    replace_data_set: List[Mapping[str, str]],
    log_flag: bool,
    clog: Type[Logger],
) -> Generator[Tuple[str, str], None, None]:
    """
    Generate formatted request information.

    This generator processes unformatted request data and applies
    parameterization replacements to create formatted request bodies.

    Args:
        req_data_set: Dictionary containing unformatted request information
        replace_data_set: List of replacement data for parameterization
        log_flag: Whether to enable logging for debugging
        clog: Custom logger object for debug output

    Yields:
        Tuple of transaction name and formatted request body string

    Example:
        >>> req_data = {"requests_info": [{"transaction": "login", "body": "user={{user}}"}]}
        >>> replace_data = [{"key": "user", "expr": r"{{user}}", "new": "testuser"}]
        >>> for trans, body in gen_requests_info(req_data, replace_data, True, logger):
        ...     print(f"Transaction: {trans}, Body: {body}")
    """
    req_data_list = req_data_set["requests_info"]
    for req_info in req_data_list:
        trans = req_info["transaction"]
        str_body = req_info["body"]
        replaced_str_body = fmt_str_with_expr(replace_data_set, str_body)
        if log_flag:
            clog.debug(f"事务名称: [{trans}]; 格式化后的请求体: {replaced_str_body}")  # type: ignore
        yield trans, replaced_str_body  # type: ignore


def reform_param_file_to_dict(
    param_file: Path,
) -> Dict[str, List[Dict[str, str]]] | None:
    """
    Convert parameter file to dictionary format.

    Transforms a CSV/TXT parameter file into a structured dictionary
    format with 'data' key containing list of row dictionaries.

    Args:
        param_file: Path to the parameter file (*.txt/*.csv)

    Returns:
        Dictionary with 'data' key containing parameter rows, or None if file doesn't exist

    Example:
        >>> param_file = Path("params.csv")
        >>> result = reform_param_file_to_dict(param_file)
        >>> print(result)  # {"data": [{"col1": "val1", "col2": "val2"}, ...]}
    """
    if param_file.exists():
        with open(param_file, "r", encoding="utf-8") as f:
            reader = csv.reader(f)
            fieldnames = next(reader)
            csv_reader = csv.DictReader(f, fieldnames=fieldnames)
            all_data = {"data": []}
            for row in csv_reader:
                each_element = {}
                for k, v in row.items():
                    each_element[k] = v
                all_data["data"].append(each_element)
        return all_data
    else:
        return None


def reform_param_file_to_list(
    param_file: Path, pure_list: bool = False, expr_key: str = ""
) -> List[Dict[str, str]] | List[str] | None:
    """
    Convert parameter file to list format.

    Transforms a CSV/TXT parameter file into a list where each row
    becomes a dictionary in the list, or optionally extracts specific
    column values into a pure string list.

    Args:
        param_file: Path to the parameter file (*.txt/*.csv)
        pure_list: Whether to return List[str] format instead of List[Dict]
        expr_key: Specific key to extract values for pure_list mode

    Returns:
        List of dictionaries or list of strings, or None if file doesn't exist

    Example:
        >>> param_file = Path("users.csv")
        >>> # Get list of dictionaries
        >>> dict_list = reform_param_file_to_list(param_file)
        >>> # Get list of usernames only
        >>> username_list = reform_param_file_to_list(param_file, pure_list=True, expr_key="username")
    """
    if param_file.exists():
        with open(param_file, mode="r", encoding="utf-8") as f:
            dict_reader = csv.DictReader(f)
            list_of_dict = list(dict_reader)
        if pure_list:
            tar_expr_key = "*" if expr_key is None else expr_key
            return search(f"[*].{tar_expr_key}", list_of_dict)
        else:
            return list_of_dict
    else:
        return None


def custom_iter_time(iter_time_conf: dict) -> Union[float, int]:
    """
    Generate custom iteration time based on configuration.

    Supports both constant iteration time and random time between
    specified ranges with configurable precision.

    Args:
        iter_time_conf: Configuration dictionary containing:
            - iter_time_fmt: "constant" or "between"
            - const_iter_time: Fixed time for constant mode
            - between_iter_time: [min, max] range for random mode
            - digits: Decimal precision for random values

    Returns:
        Calculated iteration time (int or float)

    Example:
        >>> # Constant time
        >>> config = {"iter_time_fmt": "constant", "const_iter_time": 2}
        >>> time_val = custom_iter_time(config)
        >>>
        >>> # Random time between 1.0 and 3.0 seconds
        >>> config = {"iter_time_fmt": "between", "between_iter_time": [1.0, 3.0], "digits": 2}
        >>> time_val = custom_iter_time(config)
    """
    digits = iter_time_conf.get("digits")
    digits = digits if digits else 1
    iter_time = (
        iter_time_conf.get("const_iter_time")
        if iter_time_conf.get("iter_time_fmt") == "constant"
        else round(
            random.uniform(
                iter_time_conf.get("between_iter_time", [0, 0])[0],
                iter_time_conf.get("between_iter_time", [0, 0])[1],
            ),
            digits,
        )
    )

    if iter_time is None:
        iter_time = 0

    time.sleep(iter_time)

    return iter_time


def fmt_str(ori_str: str) -> str:
    """
    Format string by converting first character to lowercase.

    Takes a string and returns it with the first character converted
    to lowercase while keeping the rest unchanged.

    Args:
        ori_str: Original string to format

    Returns:
        Formatted string with first character in lowercase

    Example:
        >>> fmt_str("HelloWorld")
        'helloWorld'
        >>> fmt_str("API_KEY")
        'aPI_KEY'
    """
    return ori_str[:1].lower() + ori_str[1:]


def fmt_stages(
    stages_data: List[Mapping[str, Union[float, int, List[str]]]],
) -> List[Dict[str, Union[float, int, List[str]]]]:
    """
    Format stages data for Custom LoadTestShape.

    Processes stage configuration data to calculate cumulative timing
    and user counts for Locust load testing scenarios.

    Args:
        stages_data: List of stage dictionaries containing:
            - time: Duration to run after user loading completes (seconds)
            - users: Number of users to load in this stage
            - spawn_rate: User loading rate (users per second)
            - user_classes: Optional list of user class names to invoke

    Returns:
        Formatted stages list with cumulative timing and user counts

    Example:
        >>> stages = [
        ...     {"time": 60, "users": 10, "spawn_rate": 2},
        ...     {"time": 120, "users": 20, "spawn_rate": 5}
        ... ]
        >>> formatted = fmt_stages(stages)
        >>> print(formatted)
        [
            {"time": 65, "users": 10, "spawn_rate": 2},
            {"time": 189, "users": 30, "spawn_rate": 5}
        ]
    """
    stages, span, total_users = [], 0, 0
    for stage in stages_data:
        users = int(stage["users"])  # type: ignore
        spawn_rate = float(stage["spawn_rate"])  # type: ignore
        spawn_time = users / spawn_rate
        span += float(stage["time"]) + spawn_time  # type: ignore
        total_users += users
        stage_data = {"time": int(span), "users": total_users, "spawn_rate": spawn_rate}
        if "user_classes" in stage:
            stage_data.update({"user_classes": stage["user_classes"]})
        stages.append(stage_data)
    return stages
