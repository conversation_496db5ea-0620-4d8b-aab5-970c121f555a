# -*- coding: utf-8 -*-
"""
@Time     :   2024/10/09 17:10:49
<AUTHOR>   <PERSON><PERSON><PERSON><PERSON> 
@Email    :   <EMAIL>
@File     :   scene_kenta_prescriptions.py
@Software :   Cursor
"""

import os
import sys
import warnings

warnings.filterwarnings("ignore")

root_path = os.path.dirname(os.path.realpath(__file__))
sys.path.append(root_path)

import time
import gevent
from pathlib import Path
from dotenv import load_dotenv, find_dotenv
from locust import HttpUser, TaskSet, events, LoadTestShape
from locust.runners import STATE_STOPPING, STATE_STOPPED, STATE_CLEANUP, WorkerRunner

from neox_locust.kenta.fe_prescriptions import FEPresTask

from locust_common.function.func_param import fmt_stages
from locust_common.param.DQueue import data_queue
from locust_common.param.ExprJmesMap import get_conf
from locust_common.param.ExprJmesMap import ExprJmespathMapping as ejm
from global_context import ExtendEJM

ejm = ExtendEJM(ejm).extend_ejm

# BackendDBConfig
load_dotenv(find_dotenv(get_conf(ejm.ENV_FILE)), verbose=True, override=True)


class MainTask(TaskSet):
    """用户执行的任务"""
    tasks = {
        FEPresTask: get_conf(ejm.PRES_TASK_WEIGHT),
    }


class User(HttpUser):
    """用户类"""
    tasks = [MainTask]

    # prescriptions data queue
    param_data_pres_queue = data_queue(ejm.PARAM_FILE_PRES.join_path())


class MyCustomShape(LoadTestShape):
    """
    time -- 持续时间，经过多少秒后，进入到下个阶段
    users -- 总用户数
    spawn_rate -- 用户加载率(即每秒加载多少用户数)
    """

    stages = fmt_stages(get_conf(ejm.STAGES))

    def tick(self):
        run_time = self.get_run_time()
        for stage in self.stages:
            if run_time < stage['time']:
                try:
                    tick_data = (stage['users'], stage['spawn_rate'], [
                        eval(user_class)
                        for user_class in stage['user_classes']
                    ])
                except KeyError:
                    tick_data = (stage['users'], stage['spawn_rate'])
                return tick_data
        return None


def checker(environment):
    while environment.runner.state not in [
            STATE_STOPPING, STATE_STOPPED, STATE_CLEANUP
    ]:
        time.sleep(1)
        fail_ratio = environment.runner.stats.total.fail_ratio
        if fail_ratio > 0.1:
            environment.runner.quit()
            return


@events.init.add_listener
def on_locust_init(environment, **_kwargs):
    if not isinstance(environment.runner, WorkerRunner):
        gevent.spawn(checker, environment)


if __name__ == '__main__':
    conf_file = "scene_kenta_prescriptions.conf"
    conf_abs_file = Path.cwd().joinpath(conf_file)
    os.system(f'locust --config {conf_abs_file}')
