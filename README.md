# Testing

NeoX 测试仓库，包含自动化测试、性能测试和相关工具集。

## 项目结构

```
testing/
├── Automation/                 # 自动化测试框架
│   ├── Windows/KENTA/         # Windows UI 自动化测试项目
│   └── requirements_auto_common.txt
├── Performance/               # 性能测试工具
│   └── Locust/               # Locust 性能测试框架
├── Tools/                    # 工具集
│   ├── NeoXMetrics/          # Prometheus 指标监控工具
│   ├── NeoXHelper/           # 多平台处理工具
│   └── NeoXBlank/            # 处方空白检查服务
└── lib/                      # 公共库
    ├── locust_common/        # Locust 通用库
    ├── neox_locust/          # NeoX Locust 扩展库
    ├── neox_test_common/     # 测试通用工具库
    ├── neox_test_win/        # Windows 自动化测试库
    └── neox_test_scenarios/  # 测试场景库
```

## 自动化测试 (Automation)

基于 pytest 的 Windows UI 自动化测试框架，专注于薬師丸賢太系统的自动化测试。

### 主要特性

- **模块化设计**：通过公共库提供可复用的测试组件
- **Windows UI 自动化**：支持 Windows 应用程序的 UI 自动化测试
- **测试报告**：集成 Allure 测试报告，支持失败截图
- **配置管理**：支持多环境配置和参数化测试

### 安装配置

```powershell
# 确保已经安装了 Python 虚拟环境（conda, virtualenv...）

cd testing\Automation

# 安装自动化测试工程通用依赖包
pip install -r requirements_auto_common.txt

# 安装自动化测试工程项目专用依赖包（以 KENTA 项目为例）
cd .\Windows\KENTA
pip install -r requirements.txt
```

### 目录结构

```powershell
Automation/
│   README.md                    # 框架详细说明文档
│   requirements_auto_common.txt # 通用依赖包
│   automation-flow-diagram.md   # 框架流程图
│
└───Windows/
    └───KENTA/                   # 薬師丸賢太系统测试项目
        │   conftest.py          # pytest fixture 配置
        │   pytest.ini           # pytest 配置文件
        │   README.md            # 项目说明文档
        │   requirements.txt     # 项目依赖
        │   runall.py           # 测试执行入口
        │
        ├───conf/               # 配置文件目录
        ├───testsuite/          # 测试用例目录
        └───report/             # 测试报告输出目录
```

### 文档

详细使用说明请参考：

- **[自动化测试框架说明文档](Automation/README.md)** - 完整的框架架构和使用指南

## 性能测试 (Performance)

### Locust 性能测试框架

[![license](https://img.shields.io/github/license/locustio/locust.svg)](https://github.com/locustio/locust/blob/master/LICENSE)
[![PyPI](https://img.shields.io/pypi/v/locust.svg)](https://pypi.org/project/locust/)
[![PyPI](https://img.shields.io/pypi/pyversions/locust.svg)](https://pypi.org/project/locust/)

Locust 是一个易于使用、可编程且可扩展的性能测试工具。您可以用常规 Python 代码定义用户行为，而不受 UI 或特定领域语言的限制。这使得 Locust 具有无限的可扩展性，对开发者非常友好。

#### 主要特性

- **Python 编程**：使用 Python 代码定义测试场景
- **分布式测试**：支持多机分布式负载测试
- **实时监控**：Web UI 实时显示测试结果
- **可扩展性**：支持自定义插件和扩展
- **TimescaleDB 集成**：支持测试数据持久化存储

#### 相关链接

* 官方网站: [locust.io](https://locust.io)
* 官方文档: [docs.locust.io](https://docs.locust.io)

#### 安装配置

```bash
# 选择合适的 requirements.txt 版本安装，一般使用最新版本
cd Performance/Locust/env/requirements && pip install -r locust_v2.32.5_requirements.txt

# 根据环境需求安装额外依赖
# 本地开发环境：
cd Performance/Locust/env/requirements && pip install -r locust_dev_requirements.txt
# 远程发压机环境：
cd Performance/Locust/env/requirements && pip install -r locust_neox_requirements.txt
```

#### 目录结构

```bash
Performance/Locust/
├── backend/                    # 后端服务
│   ├── Backend.env            # 后端配置
│   ├── README.md              # 后端说明文档
│   ├── demo_payload.json      # 示例负载数据
│   ├── neox-locust-backend.py # 后端服务脚本
│   └── neox-locust-backend.service # 系统服务配置
├── env/                       # 环境配置
│   ├── Dockerfile             # Docker 镜像配置
│   ├── docker-compose.yml     # Docker Compose 配置
│   ├── lib/                   # 本地库文件
│   │   ├── locust_common-*.whl
│   │   └── neox_locust-*.whl
│   ├── requirements/          # 依赖文件
│   │   ├── locust_dev_requirements.txt
│   │   ├── locust_neox_requirements.txt
│   │   └── locust_v2.32.5_requirements.txt
│   └── timescaledb/          # TimescaleDB 配置
│       ├── docker-compose-timescaledb.yml
│       └── sql/              # 数据库初始化脚本
├── preparation/              # 测试准备工具
│   └── genParam/            # 参数生成工具
└── scenarios/               # 测试场景
    ├── BackendDBConfig.env  # 数据库配置
    ├── Gateway/             # 网关测试场景
    ├── Kenta/              # Kenta 系统测试场景
    ├── Recognition/        # 识别服务测试场景
    └── Sample/             # 示例测试场景
        ├── README.md
        ├── start_test.sh   # 测试启动脚本
        ├── common/         # 通用模块
        ├── conf/          # 配置文件
        │   ├── locust.conf
        │   ├── locust_master.conf
        │   └── locust_worker.conf
        ├── locustfiles/   # Locust 测试文件
        │   ├── examples_locust/
        │   ├── examples_locust-plugins/
        │   └── examples_self_experience/
        ├── log/           # 日志文件
        ├── paramlist/     # 参数文件
        └── report/        # 测试报告
```

## 工具集 (Tools)

NeoX 测试相关工具集合，提供多种实用工具支持测试和生产环境需求。

### NeoXHelper

一个处理 Windows | Web | NeoX 生产环境需求的 CLI 工具。

#### 主要功能

- **Windows 环境管理**：DPI 设置、分辨率调整、目录创建
- **应用程序管理**：软件安装、卸载、配置
- **NSIPS 数据处理**：生成图表和 Excel 报告
- **配置模板生成**：快速生成配置文件模板

#### 安装配置

```bash
cd testing/Tools/NeoXHelper

# 使用 uv 设置 Python 环境（推荐）
uv venv
# 激活虚拟环境后安装依赖
uv pip install -e .[dev]

# 或使用传统方式
pip install -r requirements.txt
```

#### 目录结构

```bash
Tools/NeoXHelper/
│   neox.py                    # 主程序
│   neox.spec                  # PyInstaller 配置
│   README.md                  # 使用说明
│   requirements.txt           # 依赖文件
│   config.toml               # 配置文件
│
├───bin/
│   └───SetDpi.exe            # DPI 设置工具
├───common/                   # 通用模块
│   ├───clog.py              # 日志模块
│   ├───configuration.py     # 配置管理
│   ├───context.py           # 上下文管理
│   ├───mapping.py           # 映射工具
│   ├───powershell.py        # PowerShell 接口
│   └───variables.py         # 变量管理
├───scopes/                  # 功能模块
│   ├───replica.py           # 副本管理
│   ├───secret.py            # 密钥管理
│   └───windows.py           # Windows 操作
└───templates/               # 模板文件
    └───template_config.toml # 配置模板
```

#### 使用方法

详细使用说明请参考：**[NeoXHelper 使用文档](Tools/NeoXHelper/README.md)**

### NeoXMetrics

一个将指标写入 Prometheus 的 CLI 工具，支持 Redis TimeSeries 数据处理和监控。

#### 主要功能

- **Prometheus 指标监控**：从 Redis TimeSeries 读取数据并暴露为 Prometheus 指标
- **RTS 数据处理**：Redis TimeSeries 数据汇总、转存和对比功能
- **日本时区支持**：所有时间计算使用日本时区 (Asia/Tokyo)
- **定时任务**：支持每分钟和每小时的自动化数据处理

#### 使用方法

详细使用说明请参考：**[NeoXMetrics 使用文档](Tools/NeoXMetrics/README.md)**

### NeoXBlank

一个用于检查处方是否为空白的 HTTP 服务器。

#### 主要功能

- **图像分析**：检测处方图像是否为空白
- **HTTP API**：提供 REST API 接口
- **实时处理**：支持实时图像检查请求

#### 安装配置

```bash
cd testing/Tools/NeoXBlank

# 配置 Python 环境
pip install -r requirements.txt
```

#### 使用方法

```bash
# 启动服务器
python figure.py

# 或后台运行
nohup python figure.py > output.log 2>&1 &

# API 调用示例
curl -X POST http://localhost:6999/check_blank \
  -H "Content-Type: application/json" \
  -d '{"image_path": "path/to/image.png"}'
```

详细使用说明请参考：**[NeoXBlank 使用文档](Tools/NeoXBlank/README.md)**

## 公共库 (lib)

项目公共库目录，提供可复用的测试组件和工具，支持自动化测试和性能测试。

### 库结构

- **locust_common**：Locust 通用功能库
- **neox_locust**：NeoX 定制的 Locust 扩展库
- **neox_test_common**：测试通用工具库，提供配置管理、日志系统等基础功能
- **neox_test_win**：Windows 平台 UI 自动化测试支持库
- **neox_test_scenarios**：测试场景实现库，包含具体的业务流程自动化

### 构建方法

#### 使用 pyproject.toml（推荐）

```bash
# 安装 hatch 构建工具
pip install hatch

# 构建指定库（生成 whl 文件和源码包）
cd lib/{target-lib-dir} && hatch build
```

#### 使用 setuptools

```bash
# 确保 setuptools 版本满足需求
pip list | grep setuptools

# 构建指定库
cd lib/{target-lib-dir} && python setup.py bdist_wheel
```

详细构建说明请参考：**[库构建文档](lib/Build.md)**

## 快速开始

### 自动化测试

```bash
# 1. 安装通用依赖
cd testing/Automation
pip install -r requirements_auto_common.txt

# 2. 安装项目依赖（以 KENTA 为例）
cd Windows/KENTA
pip install -r requirements.txt

# 3. 运行测试
python runall.py
```

### 性能测试

```bash
# 1. 安装 Locust 依赖
cd testing/Performance/Locust/env/requirements
pip install -r locust_v2.32.5_requirements.txt

# 2. 进入测试场景目录
cd ../../scenarios/Sample

# 3. 启动测试
./start_test.sh
```

### 工具使用

```bash
# NeoXHelper - 生成配置模板
cd testing/Tools/NeoXHelper
python neox.py template generate --path=.

# NeoXMetrics - 启动监控
cd testing/Tools/NeoXMetrics
python neox_metrics.py

# NeoXBlank - 启动服务
cd testing/Tools/NeoXBlank
python figure.py
```

## 文档索引

### 自动化测试
- **[自动化测试框架说明](Automation/README.md)** - 完整的自动化测试框架文档
- **[自动化测试流程图](Automation/automation-flow-diagram.md)** - 测试框架流程图

### 性能测试
- **[性能测试框架说明](Performance/README.md)** - 基于 Locust 的性能测试框架完整文档
- **[性能测试工作流程](Performance/workflow.md)** - 性能测试执行流程图和详细说明

### 工具集
- **[NeoXHelper 使用文档](Tools/NeoXHelper/README.md)** - NeoXHelper 工具详细使用说明
- **[NeoXMetrics 使用文档](Tools/NeoXMetrics/README.md)** - NeoXMetrics 监控工具使用指南
- **[NeoXBlank 使用文档](Tools/NeoXBlank/README.md)** - NeoXBlank 服务使用说明

### 公共库
- **[库构建文档](lib/Build.md)** - 公共库构建方法说明

---

**注意**：本项目持续更新中，使用过程中如遇问题请及时反馈。各组件的详细使用方法请参考对应的 README 文档。
