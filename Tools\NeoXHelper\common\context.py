# -*- coding: utf-8 -*-
"""
@Date       :   2024-02-01
<AUTHOR>   <PERSON><PERSON>Lu
@Email      :   <EMAIL>
@File       :   context.py
@Software   :   PyCharm
"""
from pathlib import Path

from common.configuration import load_toml


class GlobalContext:
    """
    Global context
    """

    def __init__(self, file):
        self._file = file
        if self._file is not None:
            file_path = Path.cwd().joinpath(self._file)
            if file_path.exists():
                self._file_path = file_path
            else:
                file_path = Path(self._file).resolve()
                if file_path.exists():
                    self._file_path = file_path
                else:
                    raise Exception(f"Config Error! Your file path is not exists: [{file_path}]")

    @property
    def config(self):
        return load_toml(self._file_path) if self._file is not None else None
