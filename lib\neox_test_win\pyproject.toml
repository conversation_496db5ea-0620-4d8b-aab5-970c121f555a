[build-system]
requires = ["hatchling"]
build-backend = "hatchling.build"

[project]
name = "neox-test-win"
version = "0.0.1"
description = "The development kit of NeoX for automation testing  (Windows Lib)"
requires-python = ">=3.10"
dependencies = ["neox-test-common>=0.0.1", "pywin32>=310"]
authors = [{ name = "Kuno <PERSON>", email = "<EMAIL>" }]
readme = "README.md"
license = { text = "MIT License" }
classifiers = [
    "Development Status :: 4 - Beta",
    "Topic :: Software Development :: Libraries :: Python Modules",
    "Programming Language :: Python :: 3.11",
    "Programming Language :: Python :: 3.12",
    "Programming Language :: Python :: 3.13",
    "Operating System :: OS Independent",
]

[project.urls]
Repository = "https://bitbucket.org/neoxinc/testing/src/main/"
