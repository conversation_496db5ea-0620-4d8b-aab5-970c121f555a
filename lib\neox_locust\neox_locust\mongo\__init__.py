#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
@Date       :   2025-05-09
<AUTHOR>   <PERSON><PERSON>Lu
@Email      :   <EMAIL>
@File       :   __init__.py
@Software   :   Cursor
"""

"""
MongoDB Module

This module provides MongoDB document construction utilities and database
testing support for NeoX applications.

Components:
    mongo: Document construction utilities for prescription and survey data,
           with support for ObjectId generation, timestamp handling, and
           batch document processing for performance testing scenarios
"""
