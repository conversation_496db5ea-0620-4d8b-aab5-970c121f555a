# -*- coding: utf-8 -*-
"""
@Time     :   2025/06/26 11:22:16
<AUTHOR>   <PERSON><PERSON><PERSON><PERSON>
@Email    :   <EMAIL>
@File     :   scene_recognition_engine.py
@Software :   Cursor
"""

import os
import sys
import time
import warnings
from pathlib import Path

warnings.filterwarnings("ignore")

import gevent
from dotenv import find_dotenv, load_dotenv
from locust import HttpUser, LoadTestShape, TaskSet, events
from locust.runners import STATE_CLEANUP, STATE_STOPPED, STATE_STOPPING, WorkerRunner

local_dir = os.path.dirname(os.path.realpath(__file__))
sys.path.append(local_dir)

from global_context import ExtendEJM
from locust_common.function.func_param import fmt_stages
from locust_common.param.DQueue import data_queue
from locust_common.param.ExprJmesMap import ExprJmespathMapping as ejm
from locust_common.param.ExprJmesMap import get_conf
from neox_locust.recognition.engine import EngineTask

ejm = ExtendEJM(ejm).extend_ejm

# BackendDBConfig
load_dotenv(find_dotenv(get_conf(ejm.ENV_FILE)), verbose=True, override=True)  # type: ignore


class MainTask(TaskSet):
    """用户执行的任务"""

    tasks = {
        EngineTask: get_conf(ejm.SDE_TASK_WEIGHT),  # type: ignore
    }


class User(HttpUser):
    """用户类"""

    tasks = [MainTask]

    # images data queue
    param_data_images_queue = data_queue(ejm.PARAM_FILE_IMAGES.join_path())  # type: ignore


class MyCustomShape(LoadTestShape):
    """
    time -- 持续时间，经过多少秒后，进入到下个阶段
    users -- 总用户数
    spawn_rate -- 用户加载率(即每秒加载多少用户数)
    """

    stages = fmt_stages(get_conf(ejm.STAGES))  # type: ignore

    def tick(self):
        run_time = self.get_run_time()
        for stage in self.stages:
            if run_time < stage["time"]:  # type: ignore
                try:
                    tick_data = (
                        stage["users"],
                        stage["spawn_rate"],
                        [eval(user_class) for user_class in stage["user_classes"]],  # type: ignore
                    )
                except KeyError:
                    tick_data = (stage["users"], stage["spawn_rate"])
                return tick_data
        return None


def checker(environment):
    while environment.runner.state not in [
        STATE_STOPPING,
        STATE_STOPPED,
        STATE_CLEANUP,
    ]:
        time.sleep(1)
        fail_ratio = environment.runner.stats.total.fail_ratio
        if fail_ratio > 0.1:
            environment.runner.quit()
            return


@events.init.add_listener
def on_locust_init(environment, **_kwargs):
    if not isinstance(environment.runner, WorkerRunner):
        gevent.spawn(checker, environment)


if __name__ == "__main__":
    conf_file = "scene_recognition_engine.conf"
    conf_abs_file = Path.cwd().joinpath(conf_file)
    os.system(f"locust --config {conf_abs_file}")
