[csv]
category = "e"
output_filename = "param_data_enquete"
# output_filename = "param_data_pres"
merge = true
aggregate = true

#处方笺
# [csv.fields]
# merchant = "NeoXStressTestAccount"
# merchant_id = "6762aafdccf5c7321319c42a"
# device_id = "c5e86ac0-7e21-48f6-b40b-87c086b56cd9"
# s3_path = "prescriptionAsync/testcase"
# # 老逻辑（Kenta < v1.9.9）则将 image_category 字段注释即可
# image_category = "p"

# 调查问卷
[csv.fields]
merchant = "NeoXStressTestAccount"
merchant_id = "6762aafdccf5c7321319c42a"
device_id = "c5e86ac0-7e21-48f6-b40b-87c086b56cd9"
s3_path = "prescriptionAsync/enquetecase"
# 老逻辑（Kenta < v1.9.9）则将 image_category 字段注释即可
image_category = "e"
