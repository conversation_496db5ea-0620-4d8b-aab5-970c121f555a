# -*- coding: utf-8 -*-
"""
Yakushi Windows客户端UI自动化测试执行器

该脚本提供了灵活的测试执行选项，支持按模块、按功能执行测试。
所有测试用例已完全重构为POM（Page Object Model）模式。

@Date       :   2024-02-07
<AUTHOR>   KunoLu
@Email      :   <EMAIL>
@File       :   runall.py
@Software   :   PyCharm
"""

import argparse
import os
import sys

import pytest


class TestRunner:
    """
    Yakushi测试执行器

    提供灵活的测试执行选项，支持按模块、按功能执行测试。
    """

    def __init__(self):
        """初始化测试执行器"""
        self.test_modules = {
            "login": "./testsuite/登录登出",
            "homepage": "./testsuite/首页",
            "prescription": "./testsuite/处方笺履历",
            "nsips": "./testsuite/NSIPS突合",
            "settings": "./testsuite/设定",
            "ui": "./testsuite/UI",
            "other": "./testsuite/其他",
        }

        self.default_args = ["-vs"]

    def run_module(self, module_name: str, extra_args: list = None):
        """
        运行指定模块的测试

        Args:
            module_name (str): 模块名称
            extra_args (list): 额外的pytest参数
        """
        if module_name not in self.test_modules:
            print(f"错误：未知的测试模块 '{module_name}'")
            print(f"可用模块：{list(self.test_modules.keys())}")
            return False

        test_path = self.test_modules[module_name]
        args = self.default_args + [test_path]

        if extra_args:
            args.extend(extra_args)

        print(f"执行测试模块：{module_name} ({test_path})")
        print(f"pytest参数：{' '.join(args)}")

        return pytest.main(args) == 0

    def run_all_modules(self, extra_args: list = None):
        """
        运行所有测试模块

        Args:
            extra_args (list): 额外的pytest参数
        """
        print("执行所有测试模块...")
        success_count = 0
        total_count = len(self.test_modules)

        for module_name in self.test_modules:
            print(f"\n{'=' * 60}")
            print(f"执行模块：{module_name}")
            print(f"{'=' * 60}")

            if self.run_module(module_name, extra_args):
                success_count += 1
                print(f"✅ 模块 {module_name} 执行成功")
            else:
                print(f"❌ 模块 {module_name} 执行失败")

        print(f"\n{'=' * 60}")
        print(f"测试执行完成：{success_count}/{total_count} 个模块成功")
        print(f"{'=' * 60}")

        return success_count == total_count

    def run_specific_test(self, test_path: str, extra_args: list = None):
        """
        运行指定的测试文件或测试函数

        Args:
            test_path (str): 测试路径（文件或函数）
            extra_args (list): 额外的pytest参数
        """
        args = self.default_args + [test_path]

        if extra_args:
            args.extend(extra_args)

        print(f"执行指定测试：{test_path}")
        print(f"pytest参数：{' '.join(args)}")

        return pytest.main(args) == 0

    def generate_allure_report(
        self, results_dir: str = "./temp", report_dir: str = "./report"
    ):
        """
        生成Allure报告

        Args:
            results_dir (str): 测试结果目录
            report_dir (str): 报告输出目录
        """
        print(f"生成Allure报告：{results_dir} -> {report_dir}")

        # 生成报告
        generate_cmd = f"allure generate {results_dir} -o {report_dir} --clean"
        print(f"执行命令：{generate_cmd}")
        os.system(generate_cmd)

        # 打开报告
        open_cmd = f"allure open {report_dir}"
        print(f"执行命令：{open_cmd}")
        os.system(open_cmd)


def create_argument_parser():
    """创建命令行参数解析器"""
    parser = argparse.ArgumentParser(
        description="Yakushi Windows客户端UI自动化测试执行器",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
使用示例：
  python runall.py                           # 运行登录登出模块（默认）
  python runall.py --all                     # 运行所有模块
  python runall.py --module login            # 运行登录登出模块
  python runall.py --module homepage         # 运行首页模块
  python runall.py --module prescription     # 运行处方笺履历模块
  python runall.py --module nsips            # 运行NSIPS突合模块
  python runall.py --module settings         # 运行设定模块
  python runall.py --module ui               # 运行UI模块
  python runall.py --module other            # 运行其他模块
  python runall.py --test ./testsuite/首页/test_homepage_reviewed_list.py
  python runall.py --test ./testsuite/登录登出/test_login.py::test_login_success
  python runall.py --all --reruns 2          # 运行所有模块，失败重试2次
  python runall.py --all --report            # 运行所有模块并生成Allure报告

可用模块：
  login       - 登录登出模块
  homepage    - 首页模块
  prescription- 处方笺履历模块
  nsips       - NSIPS突合模块
  settings    - 设定模块
  ui          - UI模块
  other       - 其他模块
        """,
    )

    # 执行选项
    execution_group = parser.add_mutually_exclusive_group()
    execution_group.add_argument("--all", action="store_true", help="运行所有测试模块")
    execution_group.add_argument(
        "--module",
        choices=[
            "login",
            "homepage",
            "prescription",
            "nsips",
            "settings",
            "ui",
            "other",
        ],
        help="运行指定的测试模块",
    )
    execution_group.add_argument("--test", help="运行指定的测试文件或测试函数")

    # pytest选项
    parser.add_argument("--reruns", type=int, help="失败重试次数")
    parser.add_argument(
        "--reruns-delay", type=int, default=3, help="重试延迟时间（秒）"
    )
    parser.add_argument("--maxfail", type=int, help="最大失败次数")
    parser.add_argument(
        "--report", action="store_true", help="执行完成后生成Allure报告"
    )

    return parser


def main():
    """主函数"""
    parser = create_argument_parser()
    args = parser.parse_args()

    runner = TestRunner()

    # 构建额外的pytest参数
    extra_args = []

    if args.reruns:
        extra_args.extend(["--reruns", str(args.reruns)])
        extra_args.extend(["--reruns-delay", str(args.reruns_delay)])

    if args.maxfail:
        extra_args.extend(["--maxfail", str(args.maxfail)])

    # 添加Allure报告参数
    if args.report:
        extra_args.extend(["--alluredir", "./temp"])

    # 执行测试
    success = False

    if args.all:
        success = runner.run_all_modules(extra_args)
    elif args.module:
        success = runner.run_module(args.module, extra_args)
    elif args.test:
        success = runner.run_specific_test(args.test, extra_args)
    else:
        # 默认运行登录登出模块
        print("未指定测试模块，运行默认模块：登录登出")
        success = runner.run_module("login", extra_args)

    # 生成报告
    if args.report and success:
        runner.generate_allure_report()

    # 设置退出码
    sys.exit(0 if success else 1)


if __name__ == "__main__":
    main()
