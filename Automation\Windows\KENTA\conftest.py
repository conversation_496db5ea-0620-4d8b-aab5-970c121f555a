# -*- coding: utf-8 -*-
"""
@Date       :   2024-02-07
<AUTHOR>   <PERSON>noLu
@Email      :   <EMAIL>
@File       :   conftest.py
@Software   :   PyCharm
"""
import io
import allure
import pytest
import pyautogui as pag

from neox_test_common import ProjectConfig


@pytest.hookimpl(tryfirst=True, hookwrapper=True)
def pytest_runtest_makereport(item, call):
    """
    This hook is called for each test item and allows to modify the test report.

    Args:
        item (pytest.Item): The test item that is currently being executed.
        call (object): The information about the call, for example its outcome.

    Returns:
        object: The modified test report.
    """
    outcome = yield
    rep = outcome.get_result()

    # 以下为实现异常截图的代码：
    # rep.when可选参数有call、setup、teardown，
    # call表示为用例执行环节、setup、teardown为环境初始化和清理环节
    # 这里只针对用例执行且失败的用例进行异常截图
    if rep.when == "call" and rep.failed:
        # pyautogui.screenshot()实现截图生成图片对象后转换成二进制数据
        # allure.attach直接将截图二进制数据附加到allure报告中
        obj_ss_img = pag.screenshot()
        with io.BytesIO() as output:
            obj_ss_img.save(output, format="PNG")
            allure.attach(output.getvalue(), name="执行失败异常截图", attachment_type=allure.attachment_type.PNG)


@pytest.fixture(scope="session", name="config")
def yakushi_config_data():
    """
    Fixture: Get the toml configuration data

    Returns:
        dict: A dict containing the toml configuration data.
    """
    return ProjectConfig(["conf", "yakushi.toml"]).conf_data()
