# -*- coding: utf-8 -*-
"""
@Date       :   2024-02-07
<AUTHOR>   <PERSON><PERSON>Lu
@Email      :   <EMAIL>
@File       :   test_homepage_unreviewed_list.py
@Software   :   PyCharm
"""

import allure
from neox_test_common import logger


@allure.epic("薬師丸賢太")
@allure.suite("首页")
@allure.sub_suite("未阅览列表展示（OCR->OCR）")
@allure.feature("首页")
@allure.story("未阅览列表展示（OCR->OCR）")
@allure.title("测试用例：未阅览列表展示（OCR->OCR）")
def test_homepage_unreviewed_list_ocr2ocr(config):
    """
    TestCase: 首页 - 未阅览列表展示（OCR->OCR）
    """
    with allure.step("未阅览列表展示（OCR->OCR）"):
        logger.info("< Test :: 未阅览列表展示（OCR->OCR） >")
        assert True


@allure.epic("薬師丸賢太")
@allure.suite("首页")
@allure.sub_suite("未阅览列表展示（QR->OCR）")
@allure.feature("首页")
@allure.story("未阅览列表展示（QR->OCR）")
@allure.title("测试用例：未阅览列表展示（QR->OCR）")
def test_homepage_unreviewed_list_qr2ocr(config):
    """
    TestCase: 首页 - 未阅览列表展示（QR->OCR）
    """
    with allure.step("未阅览列表展示（QR->OCR）"):
        logger.info("< Test :: 未阅览列表展示（QR->OCR） >")
        assert True
