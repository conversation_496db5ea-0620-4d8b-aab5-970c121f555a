#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
@Date       :   2021-08-17
<AUTHOR>   Ku<PERSON><PERSON><PERSON>
@Email      :   <EMAIL>
@File       :   demo_relevance_html
@Software   :   PyCharm
"""

from locust import HttpUser, task, TaskSet, between
from lxml import etree


class WebsiteTasks(TaskSet):

    # 获取session
    def get_session(self, html):
        tags = etree.HTML(html)
        return tags.xpath("输入标签需要定位的到元素")

    # 启动
    def on_start(self):
        html = self.client.get('/index')
        session = self.get_session(html.text)
        # 设置payload参数
        payload = {
            'username': 'carl_dj',
            'password': '111111',
            'session': session

        }
        # 设置header参数
        header = {
            "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/83.0.4103.61 Safari/537.36"}
        self.client.post('/login', data=payload, headers=header)

    @task(5)
    def index(self):
        self.client.get('/')

    @task(1)
    def about(self):
        self.client.about('/about/')


class WebsiteUser(HttpUser):
    # 被测系统的host，在终端中启动locust时没有指定--host参数时才会用到
    host = "http://www.xxx.com/user/login"
    # TaskSet类，该类定义用户任务信息，必填。这里就是:WebsiteTasks类名,因为该类继承TaskSet；
    tasks = [WebsiteTasks]
    # 每个用户执行两个任务间隔时间的上下限（秒）,具体数值在上下限中随机取值
    wait_time = between(5, 15)
