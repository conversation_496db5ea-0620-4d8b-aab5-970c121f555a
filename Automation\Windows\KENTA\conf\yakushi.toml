[yakushi]

# Notice:


###################################################################

# yakushi 客户端相关信息
[yakushi.locate]
exe_abs_path = 'C:\Program Files (x86)\NeoX\Yakumaru\Yakumaru.exe'

###################################################################

# yakushi 各模块测试用例配置字段
[yakushi.modules]

###################################################################

# 通用
[yakushi.modules.common]
[yakushi.modules.common.window]
[yakushi.modules.common.window.float]
auto_id = "FloatWindow"
[yakushi.modules.common.window.main]
auto_id = "MainWebviewWindow"
[yakushi.modules.common.button]
[yakushi.modules.common.button.main]
auto_id = "ShowMainWindowBtn"
[yakushi.modules.common.button.logo]
auto_id = "FloatLogoBtn"


###################################################################

# 登录
[yakushi.modules.login]
account = "********"
password = "zbpfCgqJ"
check_cert_installed_timeout = 20

[yakushi.modules.login.control]
[yakushi.modules.login.control.box.user]
class_name = "TextBox"
auto_id = "UserTextBox"
[yakushi.modules.login.control.box.password]
class_name = "PasswordBox"
auto_id = "PasswordTextBox"
[yakushi.modules.login.control.btn.login]
btn_name = "ログイン"

###################################################################

# 登出
[yakushi.modules.logout]
[yakushi.modules.logout.button]
[yakushi.modules.logout.button.user]
auto_id = "UserBtn"
offset_y = 90

###################################################################
