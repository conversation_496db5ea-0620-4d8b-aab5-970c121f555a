#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Yakushi Windows Testing Module

This module provides Windows-specific utilities and configurations for
Yakushi application testing. It includes feature file path configurations,
screen management utilities, and Windows-specific automation tools.

The module contains:
- config: Feature file paths and test configuration constants
- screen: Screen resolution and DPI management utilities

@Date       :   2024-02-11
<AUTHOR>   KunoLu
@Email      :   <EMAIL>
@File       :   __init__.py
@Software   :   PyCharm
"""

# Import configuration constants and feature paths
from .config import FEATURES_NAME, YakushiFeaturesName

# Import screen management utilities
try:
    from .screen import Screen

    _screen_available = True
except ImportError:
    # Screen module may not be available in all environments
    _screen_available = False

# Define public API
__all__ = [
    # Configuration constants
    "YakushiFeaturesName",  # Feature file paths dataclass
    "FEATURES_NAME",  # Global features configuration instance
]

# Add Screen to __all__ if available
if _screen_available:
    __all__.append("Screen")
