# -*- coding: utf-8 -*-
"""
@Date       :   2024-02-01
<AUTHOR>   <PERSON><PERSON><PERSON>u
@Email      :   <EMAIL>
@File       :   windows.py
@Software   :   PyCharm
"""
import win32api
import win32con
import win32gui
import win32print
import pywintypes

from common.powershell import PowerShell as psl

PSL = psl('UTF-8')


def uninstall_pkg(pkg_name: str) -> tuple:
    """
    uninstall Windows pkg
    :param pkg_name: target pkg name to uninstall
    :return : command response
    """
    command = f"""Get-Package -Name "{pkg_name}" | Uninstall-Package"""
    return PSL.run(cmd=command)


def install_msi(src_msi: str, quiet: bool = False) -> tuple:
    """
    install Windows msi pkgs
    :param src_msi: target msi pkg
    :param quiet: target msi mode (passive or quiet)
    :return : command response
    """
    mode = 'passive' if not quiet else 'quiet'
    command = f"""Start-Process msiexec.exe -NoNewWindow -ArgumentList "/i `"{src_msi}`" /{mode} /norestart" -Wait"""
    return PSL.run(cmd=command)


def install_exe(src_exe: str, dst_dir: str) -> tuple:
    """
    install Windows exe pkgs
    :param src_exe: target msi pkg
    :param dst_dir: destination directory to install
    :return : command response
    """
    command = f"""Start-Process -FilePath "{src_exe}" -ArgumentList "/S", "/D={dst_dir}" -Wait"""
    return PSL.run(cmd=command)


def get_screen_size() -> tuple[int, int]:
    """
    Get the size of the screen after scaling.
    :return: A tuple containing the width and height of the screen in pixels.
    """
    w = win32api.GetSystemMetrics(0)
    h = win32api.GetSystemMetrics(1)
    return w, h


def get_real_resolution() -> tuple[int, int]:
    """
    Get the real resolution of the screen.
    This function retrieves the actual horizontal and vertical resolution of the screen,
    accounting for any scaling or other factors that may affect the displayed resolution.

    :return: A tuple containing the width and height of the screen in pixels.
    """
    hDC = win32gui.GetDC(0)  # Get the device context handle for the desktop
    # Get the horizontal and vertical resolution of the screen
    w = win32print.GetDeviceCaps(hDC, win32con.DESKTOPHORZRES)
    h = win32print.GetDeviceCaps(hDC, win32con.DESKTOPVERTRES)
    return w, h


def set_screen_resolution(tar_screen_resolution: list[int]) -> None:
    """
    Sets the screen resolution to the specified target resolution.

    :param tar_screen_resolution: A list of two integers representing the target screen resolution.
                                    The first integer is the target width, and the second integer is the target height.
    :return: None
    """
    devmode = pywintypes.DEVMODEType()

    devmode.PelsWidth = tar_screen_resolution[0]
    devmode.PelsHeight = tar_screen_resolution[1]
    devmode.Fields = win32con.DM_PELSWIDTH | win32con.DM_PELSHEIGHT
    win32api.ChangeDisplaySettings(devmode, 0)


def get_dpi() -> float:
    """
    Calculates the screen scale rate (DPI) based on the real and screen sizes.
    This function calculates the screen scale rate (DPI) by dividing the real resolution (accounting for any scaling or other factors) by the screen size. The result is then rounded to two decimal places and multiplied by 100 to get the percentage.

    :return: A float representing the screen scale rate (DPI) as a percentage.
    """
    real_resolution = get_real_resolution()
    screen_size = get_screen_size()

    screen_scale_rate = round(real_resolution[0] / screen_size[0], 2)
    screen_scale_rate = screen_scale_rate * 100
    return screen_scale_rate


def exec_setdpi(setdpi_exe_path: str, dpi: int or str) -> bool:
    """
    exec setdpi.exe
    :param setdpi_exe_path: setdpi.exe absolute path
    :param dpi: dpi param will be pass to setdpi.exe as command line args
    :return : True if successfully and False if failed
    """
    hinst = win32api.ShellExecute(0, 'open', setdpi_exe_path, str(dpi), '', 1)
    return True if hinst > 32 else False
