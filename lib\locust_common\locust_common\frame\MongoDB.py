#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
@Date       :   2025-01-14
<AUTHOR>   <PERSON><PERSON><PERSON><PERSON>
@Email      :   <EMAIL>
@File       :   MongoDB.py
@Software   :   Cursor
"""

import time
from typing import Any, Dict, List, Union

from locust import User, events
from pymongo import MongoClient
from pymongo.errors import PyMongoError

# Constants
REQUEST_TYPE_MONGODB = "MONGODB"
REQUEST_NAME_QUERY = "QUERY"


class MongoDBClient(MongoClient):
    """
    MongoDB client wrapper for Locust performance testing.

    This class extends PyMongo's MongoClient to provide Locust-compatible
    request tracking and performance metrics for MongoDB operations.
    """

    def __init__(self, conn_string: str, db_name: str):
        """
        Initialize MongoDB client with connection string and database name.

        Args:
            conn_string: MongoDB connection string
            db_name: Target database name for operations
        """
        super().__init__(conn_string)
        self.db = self[db_name]

    def execute_query(
        self,
        collection_name: str,
        operation: str,
        query: Union[Dict[str, Any], List[Dict[str, Any]]],
    ) -> None:
        """
        Execute MongoDB query with Locust event tracking.

        This method performs MongoDB operations and fires Locust events
        to track performance metrics including response time and errors.

        Args:
            collection_name: Target collection name
            operation: Operation type (find, insert_one, insert_many)
            query: Query data or document(s) to operate on

        Raises:
            ValueError: For unsupported operation types
            PyMongoError: For MongoDB-related errors
            TypeError: For invalid query data types
        """
        # 记录操作开始时间
        start_time = time.time()

        try:
            # 获取目标集合
            collection = self.db[collection_name]

            # 根据操作类型执行相应的数据库操作
            match operation:
                case "find":
                    collection.find(query)
                case "insert_one":
                    collection.insert_one(query)
                case "insert_many":
                    collection.insert_many(query)
                case _:
                    raise ValueError(
                        f"Invalid or Unsupported operation type: {operation}"
                    )

            # 计算响应时间并触发成功事件
            response_time = int((time.time() - start_time) * 1000)
            events.request.fire(
                request_type=REQUEST_TYPE_MONGODB,
                name=REQUEST_NAME_QUERY,
                response_time=response_time,
                response_length=0,
                exception=None,
                context=None,
            )

        except (PyMongoError, ValueError, TypeError) as e:
            # 计算响应时间并触发失败事件
            response_time = int((time.time() - start_time) * 1000)
            events.request.fire(
                request_type=REQUEST_TYPE_MONGODB,
                name=REQUEST_NAME_QUERY,
                response_time=response_time,
                response_length=0,
                exception=e,
                context=None,
            )


class MongoDBUser(User):
    """
    MongoDB user class for Locust testing scenarios.

    This abstract user class provides MongoDB testing capabilities
    with automatic client initialization and cleanup.
    """

    abstract = True

    def __init__(self, *args, **kwargs):
        """
        Initialize MongoDB user with client connection.

        Args:
            *args: Variable length argument list
            **kwargs: Arbitrary keyword arguments

        Note:
            Requires conn_string and db_name attributes to be defined
            in the inheriting user class.
        """
        super().__init__(*args, **kwargs)
        # 初始化 MongoDB 客户端连接
        self.client = MongoDBClient(
            conn_string=self.conn_string,  # type: ignore
            db_name=self.db_name,  # type: ignore
        )

    def on_stop(self) -> None:
        """
        Clean up MongoDB client connection when user stops.

        This method ensures proper resource cleanup by closing
        the MongoDB client connection.
        """
        # 清理 MongoDB 客户端连接
        self.client.close()
