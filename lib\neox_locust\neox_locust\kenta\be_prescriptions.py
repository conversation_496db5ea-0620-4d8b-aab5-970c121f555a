#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
@Date       :   2025-01-14
<AUTHOR>   KunoL<PERSON>
@Email      :   <EMAIL>
@File       :   be_prescriptions.py
@Software   :   Cursor
"""

from typing import Any, Dict, Optional, Type

from locust import SequentialTaskSet, task
from locust_common.function.func_param import custom_iter_time
from locust_common.param.ExprJmesMap import ExprJ<PERSON>pathMapping as ejm
from locust_common.param.ExprJmesMap import get_conf
from loguru._logger import Logger

from neox_locust.mongo.mongo import construct_document


class BEPresTask(SequentialTaskSet):
    """
    Backend prescription processing task set for Locust performance testing.

    This class simulates prescription recognition processing from the backend,
    using MongoDB operations to store and query prescription data. It supports
    parameterized testing with configurable iteration timing and logging.
    """

    def on_start(self) -> None:
        """
        Initialize task configuration when the user starts.

        Sets up MongoDB operation type, logging configuration, and other
        necessary parameters for backend prescription processing testing.
        """
        # 配置 MongoDB 执行操作的类型
        self.mongo_oper_type: str = get_conf(ejm.MONGO_OPER_TYPE)  # type: ignore

        # 配置日志处理器
        self.internal_log_flag: bool = get_conf(ejm.LOCUST_LOG_FLAG)  # type: ignore
        self.internal_log: Optional[Type[Logger]] = (
            ejm.LOCUST_LOG_FLAG.set_internal_log()
        )

    @task
    def db_query(self) -> None:
        """
        Execute MongoDB query operation for prescription data.

        This task retrieves prescription parameters from the queue, constructs
        a MongoDB document, and executes the database operation. It supports
        parameter recycling based on configuration settings.
        """
        # 从队列中获取处方参数数据
        param_data_pres_task: Dict[str, Any] = self.user.param_data_pres_queue.get()  # type: ignore

        # 构建处方的 MongoDB 查询文档
        pres_task_query = construct_document(
            operation=self.mongo_oper_type,
            log_flag=self.internal_log_flag,
            clog=self.internal_log,
            **param_data_pres_task,
        )

        # 根据参数化策略决定是否将数据放回队列（用于循环测试）
        if get_conf(ejm.PARAM_STRATEGY_PRES):  # type: ignore
            self.user.param_data_pres_queue.put_nowait(param_data_pres_task)  # type: ignore

        # 执行 MongoDB 数据库操作
        self.client.execute_query(
            self.user.collection,  # type: ignore
            self.mongo_oper_type,
            pres_task_query,
        )

    @task
    def stop(self) -> None:
        """
        Stop task to control iteration timing and execution flow.

        This task determines whether the current iteration should end based on
        scenario configuration and custom timing logic for prescription processing.
        """
        # 根据配置执行自定义迭代时间控制
        _ = custom_iter_time(get_conf(ejm.PRES_ITER_TIME_CONF))  # type: ignore

        # 根据中断配置决定是否结束当前用户的执行
        if get_conf(ejm.INTERRUPT):  # type: ignore
            self.interrupt()
