# 配置说明

## 前置条件

```bash
sudo /root/miniconda3/envs/locust_v2.32.5/bin/python -m pip install sanic
# or
sudo /root/miniconda3/envs/locust_v2.32.5/bin/python -m pip install -r */env/requirements/locust_backend_requirements.txt

# and
sudo /root/miniconda3/envs/locust_v2.32.5/bin/python -m pip install */env/lib/locust_common*
sudo /root/miniconda3/envs/locust_v2.32.5/bin/python -m pip install */env/lib/neox_locust*
```

## 使用 systemd

创建 neox-locust-backend.service 和日志文件

```bash
sudo touch /etc/systemd/system/neox-locust-backend.service
sudo touch /var/log/neox-locust-backend.access.log
sudo touch /var/log/neox-locust-backend.error.log

sudo chown ubuntu:ubuntu /var/log/neox-locust-backend.*
```

输入以下内容：

```ini
[Unit]
Description=Sanic web application for neox-locust-backend
After=network.target

[Service]
User=ubuntu
WorkingDirectory=/home/<USER>/docker-compose/locust/NeoX/backend
ExecStart=sudo /root/miniconda3/envs/locust_v2.32.5/bin/python /home/<USER>/docker-compose/locust/NeoX/backend/neox-locust-backend.py
ExecStop=/usr/bin/kill -SIGTERM $MAINPID
Restart=always
RestartSec=1
StandardOutput=file:/var/log/neox-locust-backend.access.log
StandardError=file:/var/log/neox-locust-backend.error.log

[Install]
WantedBy=multi-user.target
```


## 管理服务

```bash
sudo systemctl start neox-locust-backend.service

# 设置服务开机自启
sudo systemctl enable neox-locust-backend.service

sudo systemctl status neox-locust-backend.service

sudo systemctl restart neox-locust-backend.service

sudo systemctl stop neox-locust-backend.service
```

## 日志

```bash
sudo tail -f /var/log/neox-locust-backend.access.log
sudo tail -f /var/log/neox-locust-backend.error.log
```

## 验证

```bash
curl -X GET http://localhost:9333/locust/config/get
```
