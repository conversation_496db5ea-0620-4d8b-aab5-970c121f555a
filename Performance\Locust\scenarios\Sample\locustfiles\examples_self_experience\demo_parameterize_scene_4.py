#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
@Date       :   2021-10-31
<AUTHOR>   <PERSON><PERSON>Lu
@Email      :   <EMAIL>
@File       :   demo_parameterize_scene_4
@Software   :   PyCharm
"""
import os
import json
import time
from pathlib import Path
from locust import HttpUser, SequentialTaskSet, task, constant


class UserTask(SequentialTaskSet):

    def on_start(self):
        self.index = 0

    @task(1)
    def export_audit_trail_excel(self):
        """所有并发虚拟用户共享同一份测试数据，各虚拟用户在数据列表中循环取值。"""
        # 从 share_data 中取出数据
        param_dict = self.user.share_data[self.index]
        # 一个数除以另一个数，如比另一个数小，商数就是0，余数就是它自己
        self.index = (self.index + 1) % len(self.user.share_data)
        # API地址
        url = "/api/audit-trail/audit-log/export"
        # 请求头
        headers = {
            "Connection": "keep-alive",
            "Content-Type": "application/json",
            "Cookie": f"usr={param_dict['usr_cookie']}"
        }
        # 请求体
        body = {
            "criteria": {
                "project_id": f"{param_dict['study_id']}",
                "collect_criteria": {
                    "project_env_id": f"{param_dict['env_id']}"
                },
                "source_services": [
                    "CORE",
                    "COLLECT",
                    "CONSTRUCT"
                ]
            },
            "file_type": "EXCEL",
            "language": "zh-CN",
            "request_url": "https://perf.clicktrial.cn/",
            "export_timestamp": int(round(time.time() * 1000)),
            "role_permission_params": "{\"role_id\":\"{0}'\",\"resource_paths\":[{\"resource_path\":[{\"type\":\"Project\",\"field\": \"id\",\"value\":\"{1}\"},{\"type\":\"ProjectEnv\",\"field\":\"id\",\"value\":\"{2}\"}]}],\"permission\":\"FILE_DOWNLOAD\"}".format(
                param_dict['role_id'], param_dict['study_id'], param_dict['env_id'])
        }
        with self.client.post(name="export_audit_trail_excel", url=url, headers=headers, data=json.dumps(body),
                              catch_response=True) as response:
            # assert '.zip' in response  # 断言，判断接口返回是否成功
            response.success() if '.zip' in response.content.decode() else response.failure(
                f"FailTrans is: [export_audit_trail_excel]\nFail msg is:\n{response}")


class User(HttpUser):
    tasks = [UserTask]
    wait_time = constant(0)

    param_info = ["paramlist", "AT_040W_user_data.txt"]
    param_path = Path.cwd().parent.joinpath(param_info[0])
    param_file = param_path.joinpath(param_info[1])

    with param_file.open('r', encoding='utf-8') as f:
        param_list = f.readlines()
    key_list = param_list[0].strip().split(',')
    share_data = []
    for i, v in enumerate(param_list):
        if i > 0:
            value_list = v.strip().split(',')
            if len(key_list) == len(value_list):
                share_data.append(dict(zip(key_list, value_list)))


if __name__ == '__main__':
    path_info = ["conf", "locust.conf"]
    conf_abs_file = Path.cwd().parent.joinpath(*path_info)
    os.system(f'locust --config {conf_abs_file}')
