// The current database to use.
use('medical_user');

// Search for documents in the current collection.
// db.getCollection('prescription_task')
db.getCollection('p_task')
  .find(
    {
      // '_id': '678637e8620175f6ad1c027f',
      'merchantId': '6762aafdccf5c7321319c42a',
      'valid': 1,
      'createdAt': {
        $gte: ISODate('2025-04-18T00:00:00Z'),
        $lte: ISODate('2025-04-18T23:59:59Z')
      }
    },
  )
  .sort({ 'createdAt': -1 });