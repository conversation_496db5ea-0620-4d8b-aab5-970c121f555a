############################# config ##############################

# 执行 scene_*.py 脚本前请先配置 config.toml

############################# 参数说明 #############################

# [global]
# 1) ENV_FILE(str): 监听测试数据的后端数据库环境信息文件名，格式说明如下：
#       (1) 可指定环境信息文件，即带绝对路径或相对路径的文件名；
#       (2) 可仅输入文件名，会从当前路径依次向上级目录寻找，仅使用第一个找到的文件，读取其内部环境变量信息

# [task]
# [task.*]
# 1) task_weight(int): 对应任务集的权重

# [shape]
# 1) stages(List[Dict[int or float]]): 自定义的 LoadTestShape (可用于负载、容量、稳定性等测试场景)
#   1，当 stages 仅单个列表元素时，等价于负载测试；
#   2，当 stages 有多个列表元素时，等价于容量测试 or 自定义的特殊场景测试；
#   3，stages 中字典元素字段说明：
#       （1）time(int): 当前 stage 用户加载完毕后继续运行的时间，单位：秒；
#       （2）users(int): 当前 stage 需要加载的用户数（如存在多个 stage，total_users += each_stage.users）；
#       （3）spawn_rate(int or float): 用户加载率（即每秒加载多少用户数）；
#       （4）user_classes(Optional[List[str]]): 用户类集合（即指定执行的用户类列表）；

# [iteration]
# 1) interrupt(bool): 是否在迭代过程中重置用户行为（类似于长短连接），默认为 false（即长连接）

# [iteration.*]
# 1) iter_time_fmt(str): 思考时间的格式，支持 [ constant | between ]
# 2) const_iter_time(int/float): 固定思考时间（单位：秒），iteration.iter_time_fmt = "constant" 时生效
# 3) between_iter_time(list[float]): 指定区间内的随机思考时间（单位：秒），iteration.iter_time_fmt = "between" 时生效，如：between_iter_time = [1.0 , 10.5]

# [param_files]
# [param_files.*]
# 1) param_data_loop_enable(bool): 控制 task 下的参数化数据使用策略，可选 [ true / false ]，true 则测试数据循环使用，false 则每条数据均使用一次则不再使用(数据被用完则处于 block 状态)
# 2) base_data_filename(str): 基础参数化数据文件名（确保在测试脚本所在目录的 paramlist 目录下）

# [param]
# [param.mongo]
# 1) operation_type(str): 对 MongoDB 执行的操作类型，支持 [ find | insert_one | insert_many ]

# [log]
# 1) internal_log_flag(bool): 获取 自定义方法中的 debug 日志开关（建议调试时候使用，正式发压时关闭，否则影响性能）

###################################################################

[global]
ENV_FILE = "BackendDBConfig.env"

###################################################################

[task]
[task.prescriptions]
task_weight = 1

###################################################################

[shape]
stages = [
    { time = 60, users = 5, spawn_rate = 1 },
    # { time = 60, users = 5, spawn_rate = 1 },
    # { time = 60, users = 5, spawn_rate = 1 },
    # { time = 60, users = 5, spawn_rate = 1 },
    # { time = 120, users = 5, spawn_rate = 1 },
]

###################################################################

[iteration]
interrupt = false

[iteration.prescriptions]
iter_time_fmt = "constant"
const_iter_time = 3
between_iter_time = [10.0, 20.0]

###################################################################

[param_files]
[param_files.prescriptions]
param_data_loop_enable = true
# base_data_filename = "param_data_pres.csv"
base_data_filename = "param_data_pres-aggregated.csv"

###################################################################

[param]
[param.mongo]
operation_type = "insert_many"

###################################################################

[log]
internal_log_flag = true

###################################################################
