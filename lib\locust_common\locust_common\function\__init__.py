#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
@Date       :   2024-02-20
<AUTHOR>   <PERSON><PERSON>Lu
@Email      :   <EMAIL>
@File       :   __init__.py
@Software   :   PyCharm
"""

"""
Function Module

This module provides utility functions for common operations in Locust testing,
including configuration file handling, logging setup, and parameter processing.

Components:
    func_conf: Configuration file parsing utilities (TOML, YAML, JSON, CONF)
    func_log: Logging configuration and management utilities
    func_param: Parameter processing and data manipulation utilities
"""
