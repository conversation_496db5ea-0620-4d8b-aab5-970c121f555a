#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Logging Module

This module provides logging configuration and utilities for the NeoX testing framework.
It sets up Loguru-based logging with propagation to the standard Python logging system.

@Date       :   2024-02-11
<AUTHOR>   KunoLu
@Email      :   <EMAIL>
@File       :   __init__.py
@Software   :   PyCharm
"""

# Import logging utilities and configured logger
from .loguru import PropagateHandler, logger

# Define public API for logging module
__all__ = [
    "logger",  # Main logger instance
    "PropagateHandler",  # Custom handler for standard logging integration
]
